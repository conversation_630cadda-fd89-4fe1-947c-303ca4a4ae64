#!/usr/bin/env python3

"""
CODY v2.0 - Advanced SaaS AI Terminal Agent
A powerful, intelligent coding assistant with multi-model AI support
"""

from .core import (
    CodyAgent,
    CodyConfig,
    create_cody_agent,
    get_cody_agent,
    ProviderManager
)

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Advanced SaaS AI Terminal Agent"
__license__ = "MIT"

__all__ = [
    'CodyAgent',
    'CodyConfig', 
    'create_cody_agent',
    'get_cody_agent',
    'ProviderManager'
]
