#!/usr/bin/env python3

"""
DeepSeek AI Provider for CODY v2.0
Implementation for DeepSeek chat and reasoning models
"""

import asyncio
import json
import logging
import os
from typing import AsyncGenerator, Dict, List, Optional
import aiohttp

from .base_provider import (
    BaseAIProvider, 
    ProviderConfig, 
    ModelInfo, 
    ModelType,
    ChatRequest, 
    ChatResponse, 
    ChatMessage
)

logger = logging.getLogger('CODY.DeepSeekProvider')

class DeepSeekProvider(BaseAIProvider):
    """
    DeepSeek AI provider implementation.
    Supports DeepSeek chat and reasoning models.
    """
    
    def __init__(self, config: Optional[ProviderConfig] = None):
        """
        Initialize DeepSeek provider.
        
        Args:
            config: Provider configuration
        """
        if config is None:
            config = ProviderConfig(
                api_key=os.getenv('DEEPSEEK_API_KEY', ''),
                base_url=os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
                timeout=30.0,
                max_retries=3,
                rate_limit_per_minute=60
            )
        
        super().__init__("deepseek", config)
    
    def initialize(self) -> None:
        """Initialize DeepSeek-specific components."""
        if not self.config.api_key:
            raise ValueError("DeepSeek API key is required")
        
        # Register available models
        self.models = {
            "deepseek-chat": ModelInfo(
                name="deepseek-chat",
                display_name="DeepSeek Chat",
                model_type=ModelType.CHAT,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=True,
                cost_per_token=0.00014,
                description="DeepSeek's flagship chat model with excellent reasoning capabilities"
            ),
            "deepseek-reasoner": ModelInfo(
                name="deepseek-reasoner",
                display_name="DeepSeek Reasoner",
                model_type=ModelType.REASONING,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=False,
                cost_per_token=0.00055,
                description="DeepSeek's reasoning model with enhanced logical thinking"
            ),
            "deepseek-coder": ModelInfo(
                name="deepseek-coder",
                display_name="DeepSeek Coder",
                model_type=ModelType.CODE,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=True,
                cost_per_token=0.00014,
                description="DeepSeek's specialized coding model"
            )
        }
        
        logger.info(f"DeepSeek provider initialized with {len(self.models)} models")
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion using DeepSeek API.
        
        Args:
            request: Chat completion request
            
        Returns:
            Chat completion response
        """
        return await self._execute_with_retry(self._chat_completion_impl, request)
    
    async def _chat_completion_impl(self, request: ChatRequest) -> ChatResponse:
        """Internal implementation of chat completion."""
        # Validate request
        if not self.validate_request(request):
            raise ValueError("Invalid chat request")
        
        # Optimize request for DeepSeek
        optimized_request = self.optimize_request(request)
        
        # Prepare API request
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        headers.update(self.config.custom_headers)
        
        payload = {
            "model": optimized_request.model,
            "messages": [
                {
                    "role": msg.role,
                    "content": msg.content
                }
                for msg in optimized_request.messages
            ],
            "temperature": optimized_request.temperature,
            "top_p": optimized_request.top_p,
            "stream": False
        }
        
        if optimized_request.max_tokens:
            payload["max_tokens"] = optimized_request.max_tokens
        
        if optimized_request.stop:
            payload["stop"] = optimized_request.stop
        
        # Make API request
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(
                f"{self.config.base_url}/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"DeepSeek API error {response.status}: {error_text}")
                
                result = await response.json()
        
        # Parse response
        choice = result["choices"][0]
        usage = result.get("usage", {})
        
        return ChatResponse(
            content=choice["message"]["content"],
            model=result["model"],
            usage={
                "prompt_tokens": usage.get("prompt_tokens", 0),
                "completion_tokens": usage.get("completion_tokens", 0),
                "total_tokens": usage.get("total_tokens", 0)
            },
            finish_reason=choice.get("finish_reason", "stop"),
            response_time=0.0,  # Will be calculated by base class
            metadata={
                "provider": "deepseek",
                "request_id": result.get("id", ""),
                "created": result.get("created", 0)
            }
        )
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion.
        
        Args:
            request: Chat completion request
            
        Yields:
            Partial response chunks
        """
        # Validate request
        if not self.validate_request(request):
            raise ValueError("Invalid chat request")
        
        # Check rate limit
        if not self._check_rate_limit():
            raise Exception("Rate limit exceeded")
        
        self._record_request()
        
        # Optimize request for DeepSeek
        optimized_request = self.optimize_request(request)
        
        # Prepare API request
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        headers.update(self.config.custom_headers)
        
        payload = {
            "model": optimized_request.model,
            "messages": [
                {
                    "role": msg.role,
                    "content": msg.content
                }
                for msg in optimized_request.messages
            ],
            "temperature": optimized_request.temperature,
            "top_p": optimized_request.top_p,
            "stream": True
        }
        
        if optimized_request.max_tokens:
            payload["max_tokens"] = optimized_request.max_tokens
        
        if optimized_request.stop:
            payload["stop"] = optimized_request.stop
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
                async with session.post(
                    f"{self.config.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"DeepSeek API error {response.status}: {error_text}")
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            
                            if data == '[DONE]':
                                break
                            
                            try:
                                chunk = json.loads(data)
                                if 'choices' in chunk and chunk['choices']:
                                    delta = chunk['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield delta['content']
                            except json.JSONDecodeError:
                                continue
            
            self._record_success(0.0)  # Response time will be calculated elsewhere
            
        except Exception as e:
            self._record_failure()
            raise
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get list of available DeepSeek models."""
        return list(self.models.values())
    
    def optimize_request(self, request: ChatRequest) -> ChatRequest:
        """Optimize request for DeepSeek models."""
        optimized = ChatRequest(
            messages=request.messages.copy(),
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stream=request.stream,
            stop=request.stop,
            metadata=request.metadata.copy()
        )
        
        # DeepSeek-specific optimizations
        if request.model == "deepseek-reasoner":
            # For reasoning model, adjust temperature for better logical thinking
            optimized.temperature = min(0.3, request.temperature)
            
            # Add reasoning prompt if not present
            if not any("think step by step" in msg.content.lower() for msg in request.messages):
                if optimized.messages and optimized.messages[0].role == "system":
                    optimized.messages[0].content += "\n\nPlease think step by step and show your reasoning process."
                else:
                    optimized.messages.insert(0, ChatMessage(
                        role="system",
                        content="You are a helpful assistant. Please think step by step and show your reasoning process."
                    ))
        
        elif request.model == "deepseek-coder":
            # For coding model, add coding-specific instructions
            if optimized.messages and optimized.messages[0].role == "system":
                optimized.messages[0].content += "\n\nFocus on writing clean, efficient, and well-documented code."
            else:
                optimized.messages.insert(0, ChatMessage(
                    role="system",
                    content="You are an expert programmer. Focus on writing clean, efficient, and well-documented code."
                ))
        
        return optimized
    
    def validate_request(self, request: ChatRequest) -> bool:
        """Validate request for DeepSeek models."""
        if not super().validate_request(request):
            return False
        
        # DeepSeek-specific validations
        if request.model not in self.models:
            logger.warning(f"Unknown DeepSeek model: {request.model}")
            return False
        
        # Check for empty messages
        for msg in request.messages:
            if not msg.content.strip():
                logger.warning("Empty message content detected")
                return False
        
        return True
