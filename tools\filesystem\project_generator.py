#!/usr/bin/env python3

"""
Project Generator for CODY v2.0
Intelligent full-stack project scaffolding and structure creation
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import asyncio

from .smart_file_ops import SmartFileOperations, OperationResult

logger = logging.getLogger('CODY.ProjectGenerator')

class ProjectType(Enum):
    """Types of projects that can be generated."""
    WEB_APP = "web_app"
    API = "api"
    MOBILE_APP = "mobile_app"
    DESKTOP_APP = "desktop_app"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    DATA_SCIENCE = "data_science"
    MACHINE_LEARNING = "machine_learning"
    MICROSERVICE = "microservice"
    FULL_STACK = "full_stack"

class Framework(Enum):
    """Supported frameworks."""
    # Frontend
    REACT = "react"
    VUE = "vue"
    ANGULAR = "angular"
    SVELTE = "svelte"
    NEXT_JS = "nextjs"
    NUXT_JS = "nuxtjs"
    
    # Backend
    EXPRESS = "express"
    FASTAPI = "fastapi"
    DJANGO = "django"
    FLASK = "flask"
    SPRING_BOOT = "spring_boot"
    LARAVEL = "laravel"
    RAILS = "rails"
    
    # Mobile
    REACT_NATIVE = "react_native"
    FLUTTER = "flutter"
    IONIC = "ionic"
    
    # Desktop
    ELECTRON = "electron"
    TAURI = "tauri"
    TKINTER = "tkinter"
    
    # Data/ML
    JUPYTER = "jupyter"
    STREAMLIT = "streamlit"
    GRADIO = "gradio"

@dataclass
class ProjectTemplate:
    """Project template definition."""
    name: str
    description: str
    project_type: ProjectType
    framework: Framework
    language: str
    structure: Dict[str, Any]
    dependencies: List[str]
    dev_dependencies: List[str] = field(default_factory=list)
    scripts: Dict[str, str] = field(default_factory=dict)
    config_files: Dict[str, str] = field(default_factory=dict)
    documentation: List[str] = field(default_factory=list)

@dataclass
class ProjectConfig:
    """Project configuration."""
    name: str
    description: str
    author: str
    version: str = "1.0.0"
    license: str = "MIT"
    git_init: bool = True
    install_dependencies: bool = True
    create_venv: bool = True
    add_ci_cd: bool = False
    add_docker: bool = False
    add_tests: bool = True
    add_docs: bool = True

class ProjectGenerator:
    """
    Intelligent project generator that creates full-stack projects
    with best practices and modern tooling.
    """
    
    def __init__(self, file_ops: Optional[SmartFileOperations] = None):
        """Initialize the project generator."""
        self.file_ops = file_ops or SmartFileOperations()
        self.templates = self._initialize_templates()
        
        logger.info("Project generator initialized")
    
    def _initialize_templates(self) -> Dict[str, ProjectTemplate]:
        """Initialize project templates."""
        templates = {}
        
        # React Web App
        templates["react_app"] = ProjectTemplate(
            name="React Web Application",
            description="Modern React app with TypeScript and Tailwind CSS",
            project_type=ProjectType.WEB_APP,
            framework=Framework.REACT,
            language="typescript",
            structure={
                "src": {
                    "components": {},
                    "pages": {},
                    "hooks": {},
                    "utils": {},
                    "types": {},
                    "styles": {},
                    "assets": {
                        "images": {},
                        "icons": {}
                    }
                },
                "public": {},
                "tests": {
                    "__tests__": {},
                    "utils": {}
                },
                "docs": {}
            },
            dependencies=[
                "react", "react-dom", "typescript", "@types/react", "@types/react-dom",
                "tailwindcss", "autoprefixer", "postcss"
            ],
            dev_dependencies=[
                "vite", "@vitejs/plugin-react", "eslint", "prettier",
                "jest", "@testing-library/react", "@testing-library/jest-dom"
            ],
            scripts={
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview",
                "test": "jest",
                "lint": "eslint src --ext .ts,.tsx",
                "format": "prettier --write src"
            }
        )
        
        # FastAPI Backend
        templates["fastapi_api"] = ProjectTemplate(
            name="FastAPI REST API",
            description="Modern Python API with FastAPI and async support",
            project_type=ProjectType.API,
            framework=Framework.FASTAPI,
            language="python",
            structure={
                "app": {
                    "api": {
                        "v1": {
                            "endpoints": {}
                        }
                    },
                    "core": {},
                    "models": {},
                    "schemas": {},
                    "services": {},
                    "utils": {},
                    "db": {}
                },
                "tests": {
                    "api": {},
                    "unit": {}
                },
                "docs": {},
                "scripts": {},
                "migrations": {}
            },
            dependencies=[
                "fastapi", "uvicorn", "pydantic", "sqlalchemy", "alembic",
                "python-multipart", "python-jose", "passlib", "bcrypt"
            ],
            dev_dependencies=[
                "pytest", "pytest-asyncio", "httpx", "black", "isort",
                "flake8", "mypy", "pre-commit"
            ],
            scripts={
                "start": "uvicorn app.main:app --reload",
                "test": "pytest",
                "format": "black . && isort .",
                "lint": "flake8 app tests",
                "type-check": "mypy app"
            }
        )
        
        # Full Stack App
        templates["fullstack_app"] = ProjectTemplate(
            name="Full Stack Application",
            description="Complete full-stack app with React frontend and FastAPI backend",
            project_type=ProjectType.FULL_STACK,
            framework=Framework.REACT,
            language="typescript",
            structure={
                "frontend": {
                    "src": {
                        "components": {},
                        "pages": {},
                        "hooks": {},
                        "services": {},
                        "types": {},
                        "styles": {}
                    },
                    "public": {}
                },
                "backend": {
                    "app": {
                        "api": {},
                        "models": {},
                        "schemas": {},
                        "services": {}
                    }
                },
                "shared": {
                    "types": {},
                    "utils": {}
                },
                "docs": {},
                "scripts": {},
                "docker": {}
            },
            dependencies=[
                "react", "react-dom", "typescript", "fastapi", "uvicorn"
            ],
            dev_dependencies=[
                "vite", "pytest", "eslint", "prettier", "black"
            ]
        )
        
        # Data Science Project
        templates["data_science"] = ProjectTemplate(
            name="Data Science Project",
            description="Data science project with Jupyter notebooks and analysis tools",
            project_type=ProjectType.DATA_SCIENCE,
            framework=Framework.JUPYTER,
            language="python",
            structure={
                "data": {
                    "raw": {},
                    "processed": {},
                    "external": {}
                },
                "notebooks": {
                    "exploratory": {},
                    "analysis": {},
                    "modeling": {}
                },
                "src": {
                    "data": {},
                    "features": {},
                    "models": {},
                    "visualization": {},
                    "utils": {}
                },
                "tests": {},
                "reports": {
                    "figures": {}
                },
                "docs": {}
            },
            dependencies=[
                "pandas", "numpy", "matplotlib", "seaborn", "scikit-learn",
                "jupyter", "ipykernel", "plotly", "scipy"
            ],
            dev_dependencies=[
                "pytest", "black", "flake8", "mypy", "pre-commit"
            ]
        )
        
        return templates
    
    async def generate_project(self, 
                             template_name: str,
                             project_config: ProjectConfig,
                             output_dir: str) -> Tuple[bool, List[OperationResult], Optional[str]]:
        """
        Generate a complete project from template.
        
        Args:
            template_name: Name of the template to use
            project_config: Project configuration
            output_dir: Output directory for the project
            
        Returns:
            Tuple of (success, operation_results, error_message)
        """
        try:
            if template_name not in self.templates:
                return False, [], f"Template '{template_name}' not found"
            
            template = self.templates[template_name]
            project_path = Path(output_dir) / project_config.name
            
            # Check if project directory already exists
            if project_path.exists():
                return False, [], f"Project directory '{project_path}' already exists"
            
            results = []
            
            # Create project directory
            project_path.mkdir(parents=True, exist_ok=True)
            
            # Generate project structure
            structure_results = await self._create_project_structure(
                project_path, template.structure
            )
            results.extend(structure_results)
            
            # Generate configuration files
            config_results = await self._create_config_files(
                project_path, template, project_config
            )
            results.extend(config_results)
            
            # Generate source files
            source_results = await self._create_source_files(
                project_path, template, project_config
            )
            results.extend(source_results)
            
            # Generate documentation
            if project_config.add_docs:
                doc_results = await self._create_documentation(
                    project_path, template, project_config
                )
                results.extend(doc_results)
            
            # Initialize Git repository
            if project_config.git_init:
                git_results = await self._initialize_git(project_path)
                results.extend(git_results)
            
            # Add CI/CD configuration
            if project_config.add_ci_cd:
                cicd_results = await self._add_ci_cd(project_path, template)
                results.extend(cicd_results)
            
            # Add Docker configuration
            if project_config.add_docker:
                docker_results = await self._add_docker(project_path, template)
                results.extend(docker_results)
            
            logger.info(f"Project '{project_config.name}' generated successfully")
            return True, results, None
            
        except Exception as e:
            logger.error(f"Error generating project: {e}")
            return False, [], str(e)
    
    async def _create_project_structure(self, 
                                      project_path: Path, 
                                      structure: Dict[str, Any]) -> List[OperationResult]:
        """Create the project directory structure."""
        results = []
        
        async def create_dirs(current_path: Path, struct: Dict[str, Any]):
            for name, content in struct.items():
                dir_path = current_path / name
                dir_path.mkdir(exist_ok=True)
                
                result = OperationResult(
                    operation="create_directory",
                    success=True,
                    target_path=str(dir_path),
                    message=f"Created directory: {name}"
                )
                results.append(result)
                
                if isinstance(content, dict):
                    await create_dirs(dir_path, content)
        
        await create_dirs(project_path, structure)
        return results
    
    async def _create_config_files(self, 
                                 project_path: Path,
                                 template: ProjectTemplate,
                                 config: ProjectConfig) -> List[OperationResult]:
        """Create configuration files."""
        results = []
        
        # Package.json for Node.js projects
        if template.language in ["javascript", "typescript"]:
            package_json = {
                "name": config.name,
                "version": config.version,
                "description": config.description,
                "author": config.author,
                "license": config.license,
                "scripts": template.scripts,
                "dependencies": {dep: "latest" for dep in template.dependencies},
                "devDependencies": {dep: "latest" for dep in template.dev_dependencies}
            }
            
            result = await self.file_ops.create_file(
                str(project_path / "package.json"),
                json.dumps(package_json, indent=2)
            )
            results.append(result)
        
        # Requirements.txt for Python projects
        elif template.language == "python":
            requirements = "\n".join(template.dependencies)
            dev_requirements = "\n".join(template.dev_dependencies)
            
            result = await self.file_ops.create_file(
                str(project_path / "requirements.txt"),
                requirements
            )
            results.append(result)
            
            if dev_requirements:
                result = await self.file_ops.create_file(
                    str(project_path / "requirements-dev.txt"),
                    dev_requirements
                )
                results.append(result)
        
        # .gitignore
        gitignore_content = self._generate_gitignore(template)
        result = await self.file_ops.create_file(
            str(project_path / ".gitignore"),
            gitignore_content
        )
        results.append(result)
        
        # README.md
        readme_content = self._generate_readme(template, config)
        result = await self.file_ops.create_file(
            str(project_path / "README.md"),
            readme_content
        )
        results.append(result)
        
        return results
    
    async def _create_source_files(self, 
                                 project_path: Path,
                                 template: ProjectTemplate,
                                 config: ProjectConfig) -> List[OperationResult]:
        """Create basic source files."""
        results = []
        
        # Create main entry point based on framework
        if template.framework == Framework.REACT:
            # Create main App component
            app_content = self._generate_react_app()
            result = await self.file_ops.create_file(
                str(project_path / "src" / "App.tsx"),
                app_content
            )
            results.append(result)
            
            # Create index file
            index_content = self._generate_react_index()
            result = await self.file_ops.create_file(
                str(project_path / "src" / "index.tsx"),
                index_content
            )
            results.append(result)
        
        elif template.framework == Framework.FASTAPI:
            # Create main FastAPI app
            main_content = self._generate_fastapi_main()
            result = await self.file_ops.create_file(
                str(project_path / "app" / "main.py"),
                main_content
            )
            results.append(result)
        
        return results
    
    async def _create_documentation(self, 
                                  project_path: Path,
                                  template: ProjectTemplate,
                                  config: ProjectConfig) -> List[OperationResult]:
        """Create project documentation."""
        results = []
        
        # Create basic documentation structure
        docs_content = f"""# {config.name} Documentation

## Overview
{config.description}

## Getting Started

### Prerequisites
- {template.language.title()}
- {template.framework.value}

### Installation
1. Clone the repository
2. Install dependencies
3. Run the application

## Project Structure
This project follows best practices for {template.framework.value} applications.

## Contributing
Please read our contributing guidelines before submitting pull requests.

## License
This project is licensed under the {config.license} License.
"""
        
        result = await self.file_ops.create_file(
            str(project_path / "docs" / "README.md"),
            docs_content
        )
        results.append(result)
        
        return results
    
    async def _initialize_git(self, project_path: Path) -> List[OperationResult]:
        """Initialize Git repository."""
        results = []
        
        try:
            # This would normally use git commands
            # For now, just create .git directory
            git_dir = project_path / ".git"
            git_dir.mkdir(exist_ok=True)
            
            result = OperationResult(
                operation="git_init",
                success=True,
                target_path=str(project_path),
                message="Git repository initialized"
            )
            results.append(result)
            
        except Exception as e:
            result = OperationResult(
                operation="git_init",
                success=False,
                target_path=str(project_path),
                error=str(e)
            )
            results.append(result)
        
        return results
    
    async def _add_ci_cd(self, project_path: Path, template: ProjectTemplate) -> List[OperationResult]:
        """Add CI/CD configuration."""
        results = []
        
        # GitHub Actions workflow
        workflow_content = self._generate_github_workflow(template)
        workflow_path = project_path / ".github" / "workflows" / "ci.yml"
        workflow_path.parent.mkdir(parents=True, exist_ok=True)
        
        result = await self.file_ops.create_file(
            str(workflow_path),
            workflow_content
        )
        results.append(result)
        
        return results
    
    async def _add_docker(self, project_path: Path, template: ProjectTemplate) -> List[OperationResult]:
        """Add Docker configuration."""
        results = []
        
        # Dockerfile
        dockerfile_content = self._generate_dockerfile(template)
        result = await self.file_ops.create_file(
            str(project_path / "Dockerfile"),
            dockerfile_content
        )
        results.append(result)
        
        # Docker Compose
        compose_content = self._generate_docker_compose(template)
        result = await self.file_ops.create_file(
            str(project_path / "docker-compose.yml"),
            compose_content
        )
        results.append(result)
        
        return results
    
    def _generate_gitignore(self, template: ProjectTemplate) -> str:
        """Generate .gitignore content based on template."""
        if template.language == "python":
            return """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
"""
        elif template.language in ["javascript", "typescript"]:
            return """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production
build/
dist/

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db
"""
        else:
            return """# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build
build/
dist/
"""
    
    def _generate_readme(self, template: ProjectTemplate, config: ProjectConfig) -> str:
        """Generate README.md content."""
        return f"""# {config.name}

{config.description}

## Technology Stack

- **Framework**: {template.framework.value}
- **Language**: {template.language.title()}
- **Type**: {template.project_type.value.replace('_', ' ').title()}

## Getting Started

### Prerequisites

Make sure you have the following installed:
- {template.language.title()}
- Package manager (npm/yarn for Node.js, pip for Python)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd {config.name}
   ```

2. Install dependencies:
   ```bash
   # For Node.js projects
   npm install
   
   # For Python projects
   pip install -r requirements.txt
   ```

3. Start the development server:
   ```bash
   # Check package.json scripts for available commands
   npm run dev
   ```

## Project Structure

This project follows best practices and conventions for {template.framework.value} applications.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the {config.license} License - see the LICENSE file for details.

## Author

{config.author}
"""
    
    def _generate_react_app(self) -> str:
        """Generate React App component."""
        return """import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to Your React App</h1>
        <p>
          Edit <code>src/App.tsx</code> and save to reload.
        </p>
      </header>
    </div>
  );
}

export default App;
"""
    
    def _generate_react_index(self) -> str:
        """Generate React index file."""
        return """import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
"""
    
    def _generate_fastapi_main(self) -> str:
        """Generate FastAPI main file."""
        return """from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="Your API",
    description="A FastAPI application",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
"""
    
    def _generate_github_workflow(self, template: ProjectTemplate) -> str:
        """Generate GitHub Actions workflow."""
        if template.language == "python":
            return """name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: pytest
    
    - name: Run linting
      run: flake8 app tests
"""
        else:
            return """name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build
      run: npm run build
"""
    
    def _generate_dockerfile(self, template: ProjectTemplate) -> str:
        """Generate Dockerfile."""
        if template.language == "python":
            return """FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
"""
        else:
            return """FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
"""
    
    def _generate_docker_compose(self, template: ProjectTemplate) -> str:
        """Generate docker-compose.yml."""
        return """version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
"""
    
    def get_available_templates(self) -> Dict[str, str]:
        """Get list of available templates."""
        return {
            name: template.description 
            for name, template in self.templates.items()
        }
    
    def get_template_info(self, template_name: str) -> Optional[ProjectTemplate]:
        """Get detailed information about a template."""
        return self.templates.get(template_name)
