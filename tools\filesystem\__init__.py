#!/usr/bin/env python3

"""
File System Tools for CODY v2.0
Smart file operations and project generation
"""

from .smart_file_ops import (
    SmartFileOperations,
    FileOperation,
    FileType,
    FileInfo,
    OperationResult
)

from .project_generator import (
    ProjectGenerator,
    ProjectType,
    Framework,
    ProjectTemplate,
    ProjectConfig
)

__all__ = [
    'SmartFileOperations',
    'FileOperation',
    'FileType', 
    'FileInfo',
    'OperationResult',
    'ProjectGenerator',
    'ProjectType',
    'Framework',
    'ProjectTemplate',
    'ProjectConfig'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Smart file system operations and project generation"
