#!/usr/bin/env python3

"""
URL Fetcher for CODY v2.0
Advanced URL content retrieval with intelligent parsing
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import re
import hashlib

logger = logging.getLogger('CODY.URLFetcher')

class ContentType(Enum):
    """Types of web content."""
    HTML = "html"
    JSON = "json"
    XML = "xml"
    TEXT = "text"
    MARKDOWN = "markdown"
    CODE = "code"
    PDF = "pdf"
    UNKNOWN = "unknown"

@dataclass
class FetchedContent:
    """Represents fetched web content."""
    url: str
    title: str
    content: str
    content_type: ContentType
    status_code: int
    headers: Dict[str, str]
    metadata: Dict[str, Any] = field(default_factory=dict)
    fetch_time: float = field(default_factory=time.time)
    content_hash: str = ""

class URLFetcher:
    """
    Advanced URL content fetcher with intelligent parsing.
    
    Features:
    - Multiple content type support
    - Content extraction and cleaning
    - Metadata extraction
    - Caching and rate limiting
    - Error handling and retries
    """
    
    def __init__(self, cache_size: int = 100, rate_limit: float = 1.0):
        """Initialize the URL fetcher."""
        self.cache_size = cache_size
        self.rate_limit = rate_limit
        self.cache: Dict[str, FetchedContent] = {}
        self.last_request_time = 0.0
        
        # Content extractors
        self.extractors = self._initialize_extractors()
        
        logger.info("URL fetcher initialized")
    
    def _initialize_extractors(self) -> Dict[ContentType, callable]:
        """Initialize content extractors for different types."""
        return {
            ContentType.HTML: self._extract_html_content,
            ContentType.JSON: self._extract_json_content,
            ContentType.XML: self._extract_xml_content,
            ContentType.TEXT: self._extract_text_content,
            ContentType.MARKDOWN: self._extract_markdown_content,
            ContentType.CODE: self._extract_code_content
        }
    
    async def fetch_url(self, url: str, force_refresh: bool = False) -> Optional[FetchedContent]:
        """
        Fetch content from a URL.
        
        Args:
            url: URL to fetch
            force_refresh: Whether to bypass cache
            
        Returns:
            Fetched content or None if failed
        """
        # Check cache first
        if not force_refresh and url in self.cache:
            logger.info(f"Returning cached content for: {url}")
            return self.cache[url]
        
        # Rate limiting
        await self._apply_rate_limit()
        
        try:
            # Simulate HTTP request (in real implementation, use aiohttp)
            content = await self._simulate_fetch(url)
            
            if content:
                # Cache the result
                self._cache_content(url, content)
                
            return content
            
        except Exception as e:
            logger.error(f"Failed to fetch URL {url}: {e}")
            return None
    
    async def _simulate_fetch(self, url: str) -> Optional[FetchedContent]:
        """Simulate fetching URL content."""
        # This would use aiohttp in real implementation
        # For now, simulate based on URL patterns
        
        if "github.com" in url:
            return self._simulate_github_content(url)
        elif "stackoverflow.com" in url:
            return self._simulate_stackoverflow_content(url)
        elif "docs." in url or "documentation" in url:
            return self._simulate_documentation_content(url)
        else:
            return self._simulate_generic_content(url)
    
    def _simulate_github_content(self, url: str) -> FetchedContent:
        """Simulate GitHub content."""
        if "/blob/" in url and url.endswith(('.py', '.js', '.java', '.go', '.rs')):
            # Code file
            content = f"""# Example code from {url}

def example_function():
    '''This is an example function from GitHub.'''
    return "Hello from GitHub!"

class ExampleClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value
"""
            return FetchedContent(
                url=url,
                title="GitHub Code Example",
                content=content,
                content_type=ContentType.CODE,
                status_code=200,
                headers={"content-type": "text/plain"},
                metadata={"language": "python", "repository": "example/repo"}
            )
        else:
            # README or documentation
            content = f"""# Project Documentation

This is documentation from {url}.

## Features
- Feature 1: Advanced functionality
- Feature 2: Easy to use
- Feature 3: Well documented

## Installation
```bash
npm install example-package
```

## Usage
```javascript
const example = require('example-package');
example.doSomething();
```
"""
            return FetchedContent(
                url=url,
                title="GitHub Repository Documentation",
                content=content,
                content_type=ContentType.MARKDOWN,
                status_code=200,
                headers={"content-type": "text/markdown"}
            )
    
    def _simulate_stackoverflow_content(self, url: str) -> FetchedContent:
        """Simulate Stack Overflow content."""
        content = f"""# Stack Overflow Question

**Question:** How to solve this programming problem?

I'm trying to implement a feature but running into issues. Here's my code:

```python
def problematic_function():
    # This doesn't work as expected
    return None
```

**Answer (Accepted):**

The issue is with your approach. Here's the correct way:

```python
def correct_function():
    # This is the right way to do it
    return "Success!"
```

**Explanation:**
The problem was that you weren't handling the edge cases properly. 
The corrected version addresses these issues by...

**Additional Resources:**
- [Official Documentation]({url})
- [Related Tutorial]({url})
"""
        
        return FetchedContent(
            url=url,
            title="Stack Overflow - Programming Solution",
            content=content,
            content_type=ContentType.HTML,
            status_code=200,
            headers={"content-type": "text/html"},
            metadata={"votes": 42, "answers": 3, "views": 1234}
        )
    
    def _simulate_documentation_content(self, url: str) -> FetchedContent:
        """Simulate documentation content."""
        content = f"""# API Documentation

## Overview
This documentation covers the API endpoints and usage examples.

## Authentication
All API requests require authentication using API keys:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" {url}
```

## Endpoints

### GET /api/users
Retrieve a list of users.

**Parameters:**
- `limit` (optional): Number of users to return (default: 10)
- `offset` (optional): Number of users to skip (default: 0)

**Response:**
```json
{{
  "users": [
    {{"id": 1, "name": "John Doe", "email": "<EMAIL>"}},
    {{"id": 2, "name": "Jane Smith", "email": "<EMAIL>"}}
  ],
  "total": 100
}}
```

### POST /api/users
Create a new user.

**Request Body:**
```json
{{
  "name": "New User",
  "email": "<EMAIL>"
}}
```

## Error Handling
The API returns standard HTTP status codes:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Internal Server Error
"""
        
        return FetchedContent(
            url=url,
            title="API Documentation",
            content=content,
            content_type=ContentType.MARKDOWN,
            status_code=200,
            headers={"content-type": "text/markdown"},
            metadata={"version": "v1.0", "last_updated": "2024-01-15"}
        )
    
    def _simulate_generic_content(self, url: str) -> FetchedContent:
        """Simulate generic web content."""
        content = f"""# Web Page Content

This is content from {url}.

## Main Content
Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

### Key Points
1. Important information about the topic
2. Detailed explanations and examples
3. Best practices and recommendations

### Code Examples
```javascript
function example() {{
    console.log("Hello from {url}");
}}
```

## Conclusion
This content provides valuable information about the topic discussed.
"""
        
        return FetchedContent(
            url=url,
            title="Web Page Title",
            content=content,
            content_type=ContentType.HTML,
            status_code=200,
            headers={"content-type": "text/html"}
        )
    
    async def _apply_rate_limit(self):
        """Apply rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit:
            sleep_time = self.rate_limit - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _cache_content(self, url: str, content: FetchedContent):
        """Cache fetched content."""
        # Generate content hash
        content.content_hash = hashlib.md5(content.content.encode()).hexdigest()
        
        # Add to cache
        self.cache[url] = content
        
        # Maintain cache size
        if len(self.cache) > self.cache_size:
            # Remove oldest entry
            oldest_url = min(self.cache.keys(), 
                           key=lambda k: self.cache[k].fetch_time)
            del self.cache[oldest_url]
    
    def _extract_html_content(self, raw_content: str) -> str:
        """Extract clean text from HTML content."""
        # Simple HTML tag removal (in real implementation, use BeautifulSoup)
        clean_content = re.sub(r'<[^>]+>', '', raw_content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()
        return clean_content
    
    def _extract_json_content(self, raw_content: str) -> str:
        """Extract and format JSON content."""
        try:
            import json
            parsed = json.loads(raw_content)
            return json.dumps(parsed, indent=2)
        except:
            return raw_content
    
    def _extract_xml_content(self, raw_content: str) -> str:
        """Extract content from XML."""
        # Simple XML processing
        return raw_content
    
    def _extract_text_content(self, raw_content: str) -> str:
        """Extract plain text content."""
        return raw_content.strip()
    
    def _extract_markdown_content(self, raw_content: str) -> str:
        """Extract and clean markdown content."""
        return raw_content.strip()
    
    def _extract_code_content(self, raw_content: str) -> str:
        """Extract and format code content."""
        return raw_content.strip()
    
    async def fetch_multiple_urls(self, urls: List[str]) -> List[Optional[FetchedContent]]:
        """Fetch multiple URLs concurrently."""
        tasks = [self.fetch_url(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        return [result if not isinstance(result, Exception) else None 
                for result in results]
    
    def get_cached_urls(self) -> List[str]:
        """Get list of cached URLs."""
        return list(self.cache.keys())
    
    def clear_cache(self):
        """Clear the content cache."""
        self.cache.clear()
        logger.info("URL cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_urls": len(self.cache),
            "cache_size_limit": self.cache_size,
            "cache_hit_ratio": 0.85,  # Simulated
            "total_requests": 100,    # Simulated
            "cache_hits": 85          # Simulated
        }
