#!/usr/bin/env python3

"""
RAG (Retrieval-Augmented Generation) System for CODY v2.0
Advanced information retrieval and context integration
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import re

from .web_search import WebSearchEngine, SearchQuery, SearchResult
from .url_fetcher import <PERSON><PERSON><PERSON><PERSON><PERSON>, FetchedContent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('CODY.RAGSystem')

class DocumentType(Enum):
    """Types of documents in the knowledge base."""
    DOCUMENTATION = "documentation"
    TUTORIAL = "tutorial"
    CODE_EXAMPLE = "code_example"
    API_REFERENCE = "api_reference"
    BLOG_POST = "blog_post"
    FORUM_POST = "forum_post"
    OFFICIAL_GUIDE = "official_guide"

@dataclass
class KnowledgeChunk:
    """Represents a chunk of knowledge."""
    id: str
    content: str
    source_url: str
    document_type: DocumentType
    title: str
    relevance_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)

@dataclass
class RAGQuery:
    """Represents a RAG query."""
    question: str
    context: str = ""
    max_chunks: int = 5
    min_relevance: float = 0.3
    preferred_sources: List[str] = field(default_factory=list)
    document_types: List[DocumentType] = field(default_factory=list)

@dataclass
class RAGResponse:
    """Represents a RAG response."""
    answer: str
    sources: List[KnowledgeChunk]
    confidence: float
    query: RAGQuery
    processing_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class RAGSystem:
    """
    Retrieval-Augmented Generation system for enhanced coding assistance.
    
    Features:
    - Web search integration
    - Document retrieval and chunking
    - Context-aware answer generation
    - Source attribution and verification
    - Knowledge base caching
    """
    
    def __init__(self, 
                 search_engine: Optional[WebSearchEngine] = None,
                 url_fetcher: Optional[URLFetcher] = None):
        """Initialize the RAG system."""
        self.search_engine = search_engine or WebSearchEngine()
        self.url_fetcher = url_fetcher or URLFetcher()
        
        # Knowledge base
        self.knowledge_base: Dict[str, KnowledgeChunk] = {}
        self.max_knowledge_base_size = 1000
        
        # Document processors
        self.processors = self._initialize_processors()
        
        logger.info("RAG system initialized")
    
    def _initialize_processors(self) -> Dict[DocumentType, callable]:
        """Initialize document processors for different types."""
        return {
            DocumentType.DOCUMENTATION: self._process_documentation,
            DocumentType.TUTORIAL: self._process_tutorial,
            DocumentType.CODE_EXAMPLE: self._process_code_example,
            DocumentType.API_REFERENCE: self._process_api_reference,
            DocumentType.BLOG_POST: self._process_blog_post,
            DocumentType.FORUM_POST: self._process_forum_post,
            DocumentType.OFFICIAL_GUIDE: self._process_official_guide
        }
    
    async def query(self, rag_query: RAGQuery) -> RAGResponse:
        """
        Process a RAG query and generate response.
        
        Args:
            rag_query: The RAG query to process
            
        Returns:
            RAG response with answer and sources
        """
        start_time = time.time()
        
        try:
            logger.info(f"RAG query: {rag_query}")
            # Step 1: Retrieve relevant documents
            relevant_chunks = await self._retrieve_documents(rag_query)
            logger.info(f"Relevant chunks: {relevant_chunks}")
            
            # Step 2: Rank and filter chunks
            filtered_chunks = self._rank_and_filter_chunks(relevant_chunks, rag_query)
            logger.info(f"Filtered chunks: {filtered_chunks}")
            
            # Step 3: Generate answer using retrieved context
            answer = await self._generate_answer(rag_query, filtered_chunks)
            logger.info(f"Answer: {answer}")
            
            # Step 4: Calculate confidence
            confidence = self._calculate_confidence(rag_query, filtered_chunks, answer)
            logger.info(f"Confidence: {confidence}")
            
            processing_time = time.time() - start_time
            
            return RAGResponse(
                answer=answer,
                sources=filtered_chunks,
                confidence=confidence,
                query=rag_query,
                processing_time=processing_time,
                metadata={
                    "total_chunks_found": len(relevant_chunks),
                    "chunks_used": len(filtered_chunks),
                    "search_performed": True
                }
            )
            
        except Exception as e:
            logger.error(f"RAG query failed: {e}", exc_info=True)
            
            # Return fallback response
            return RAGResponse(
                answer=f"I apologize, but I encountered an error while searching for information about: {rag_query.question}",
                sources=[],
                confidence=0.0,
                query=rag_query,
                processing_time=time.time() - start_time,
                metadata={"error": str(e)}
            )
    
    async def _retrieve_documents(self, rag_query: RAGQuery) -> List[KnowledgeChunk]:
        """Retrieve relevant documents for the query."""
        chunks = []
        
        # Step 1: Search existing knowledge base
        kb_chunks = self._search_knowledge_base(rag_query.question)
        chunks.extend(kb_chunks)
        
        # Step 2: Perform web search if needed
        if len(chunks) < rag_query.max_chunks:
            web_chunks = await self._search_web_documents(rag_query)
            chunks.extend(web_chunks)
        
        return chunks
    
    def _search_knowledge_base(self, query: str) -> List[KnowledgeChunk]:
        """Search the existing knowledge base."""
        query_terms = query.lower().split()
        matching_chunks = []
        
        for chunk in self.knowledge_base.values():
            # Simple keyword matching
            content_lower = chunk.content.lower()
            title_lower = chunk.title.lower()
            
            # Calculate relevance score
            relevance = 0.0
            for term in query_terms:
                if term in content_lower:
                    relevance += 0.1
                if term in title_lower:
                    relevance += 0.2
            
            if relevance > 0:
                chunk.relevance_score = relevance
                matching_chunks.append(chunk)
        
        # Sort by relevance
        matching_chunks.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return matching_chunks[:5]
    
    async def _search_web_documents(self, rag_query: RAGQuery) -> List[KnowledgeChunk]:
        """Search web documents and convert to knowledge chunks."""
        # Create search query
        search_query = SearchQuery(
            query=rag_query.question,
            max_results=10
        )
        
        # Perform web search
        search_results = await self.search_engine.search(search_query)
        
        # Fetch and process documents
        chunks = []
        for result in search_results[:5]:  # Limit to top 5 results
            try:
                # Fetch document content
                content = await self.url_fetcher.fetch_url(result.url)
                
                if content:
                    # Convert to knowledge chunks
                    document_chunks = await self._process_document(content, result)
                    chunks.extend(document_chunks)
                    
                    # Add to knowledge base
                    for chunk in document_chunks:
                        self._add_to_knowledge_base(chunk)
                        
            except Exception as e:
                logger.warning(f"Failed to process document {result.url}: {e}")
        
        return chunks
    
    async def _process_document(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process a document into knowledge chunks."""
        # Determine document type
        doc_type = self._classify_document_type(content, search_result)
        
        # Get appropriate processor
        processor = self.processors.get(doc_type, self._process_generic_document)
        
        # Process document
        chunks = processor(content, search_result)
        
        return chunks
    
    def _classify_document_type(self, content: FetchedContent, search_result: SearchResult) -> DocumentType:
        """Classify the type of document."""
        url = content.url.lower()
        title = content.title.lower()
        
        if "docs." in url or "documentation" in url:
            return DocumentType.DOCUMENTATION
        elif "tutorial" in title or "how to" in title:
            return DocumentType.TUTORIAL
        elif "github.com" in url and "/blob/" in url:
            return DocumentType.CODE_EXAMPLE
        elif "api" in url or "reference" in url:
            return DocumentType.API_REFERENCE
        elif "stackoverflow.com" in url:
            return DocumentType.FORUM_POST
        elif "blog" in url or "medium.com" in url:
            return DocumentType.BLOG_POST
        else:
            return DocumentType.OFFICIAL_GUIDE
    
    def _process_documentation(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process documentation content."""
        # Split content into logical sections
        sections = self._split_into_sections(content.content)
        
        chunks = []
        for i, section in enumerate(sections):
            if len(section.strip()) > 100:  # Only meaningful sections
                chunk = KnowledgeChunk(
                    id=f"{content.url}#section_{i}",
                    content=section,
                    source_url=content.url,
                    document_type=DocumentType.DOCUMENTATION,
                    title=f"{content.title} - Section {i+1}",
                    relevance_score=search_result.relevance_score,
                    metadata={
                        "section_index": i,
                        "total_sections": len(sections),
                        "content_type": content.content_type.value
                    }
                )
                chunks.append(chunk)
        
        return chunks
    
    def _process_tutorial(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process tutorial content."""
        # Extract steps from tutorial
        steps = self._extract_tutorial_steps(content.content)
        
        chunks = []
        for i, step in enumerate(steps):
            chunk = KnowledgeChunk(
                id=f"{content.url}#step_{i}",
                content=step,
                source_url=content.url,
                document_type=DocumentType.TUTORIAL,
                title=f"{content.title} - Step {i+1}",
                relevance_score=search_result.relevance_score,
                metadata={"step_number": i+1, "total_steps": len(steps)}
            )
            chunks.append(chunk)
        
        return chunks
    
    def _process_code_example(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process code example content."""
        # Extract code blocks and explanations
        code_blocks = self._extract_code_blocks(content.content)
        
        chunks = []
        for i, code_block in enumerate(code_blocks):
            chunk = KnowledgeChunk(
                id=f"{content.url}#code_{i}",
                content=code_block,
                source_url=content.url,
                document_type=DocumentType.CODE_EXAMPLE,
                title=f"{content.title} - Code Example {i+1}",
                relevance_score=search_result.relevance_score,
                metadata={"code_block_index": i, "language": "python"}  # Simplified
            )
            chunks.append(chunk)
        
        return chunks
    
    def _process_api_reference(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process API reference content."""
        return self._process_generic_document(content, search_result)
    
    def _process_blog_post(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process blog post content."""
        return self._process_generic_document(content, search_result)
    
    def _process_forum_post(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process forum post content."""
        return self._process_generic_document(content, search_result)
    
    def _process_official_guide(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process official guide content."""
        return self._process_generic_document(content, search_result)
    
    def _process_generic_document(self, content: FetchedContent, search_result: SearchResult) -> List[KnowledgeChunk]:
        """Process generic document content."""
        # Simple chunking by paragraphs
        paragraphs = [p.strip() for p in content.content.split('\n\n') if p.strip()]
        
        chunks = []
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 50:  # Only meaningful paragraphs
                chunk = KnowledgeChunk(
                    id=f"{content.url}#para_{i}",
                    content=paragraph,
                    source_url=content.url,
                    document_type=DocumentType.OFFICIAL_GUIDE,
                    title=f"{content.title} - Paragraph {i+1}",
                    relevance_score=search_result.relevance_score,
                    metadata={"paragraph_index": i}
                )
                chunks.append(chunk)
        
        return chunks[:3]  # Limit to top 3 paragraphs
    
    def _split_into_sections(self, content: str) -> List[str]:
        """Split content into logical sections."""
        # Simple section splitting by headers
        sections = re.split(r'\n#+\s+', content)
        return [section.strip() for section in sections if section.strip()]
    
    def _extract_tutorial_steps(self, content: str) -> List[str]:
        """Extract steps from tutorial content."""
        # Look for numbered steps or step indicators
        step_patterns = [
            r'(?:Step\s+\d+|^\d+\.)\s*(.+?)(?=(?:Step\s+\d+|^\d+\.)|$)',
            r'(?:##\s+Step\s+\d+|###\s+\d+\.)\s*(.+?)(?=(?:##\s+Step\s+\d+|###\s+\d+\.)|$)'
        ]
        
        steps = []
        for pattern in step_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            if matches:
                steps.extend([match.strip() for match in matches])
                break
        
        # Fallback: split by paragraphs
        if not steps:
            steps = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        return steps[:10]  # Limit to 10 steps
    
    def _extract_code_blocks(self, content: str) -> List[str]:
        """Extract code blocks from content."""
        # Extract code blocks (markdown style)
        code_pattern = r'```(?:\w+)?\n(.*?)\n```'
        code_blocks = re.findall(code_pattern, content, re.DOTALL)
        
        # Also look for indented code blocks
        if not code_blocks:
            lines = content.split('\n')
            current_block = []
            code_blocks = []
            
            for line in lines:
                if line.startswith('    ') or line.startswith('\t'):
                    current_block.append(line)
                else:
                    if current_block:
                        code_blocks.append('\n'.join(current_block))
                        current_block = []
            
            if current_block:
                code_blocks.append('\n'.join(current_block))
        
        return code_blocks[:5]  # Limit to 5 code blocks
    
    def _rank_and_filter_chunks(self, chunks: List[KnowledgeChunk], rag_query: RAGQuery) -> List[KnowledgeChunk]:
        """Rank and filter knowledge chunks."""
        # Filter by minimum relevance
        filtered_chunks = [
            chunk for chunk in chunks 
            if chunk.relevance_score >= rag_query.min_relevance
        ]
        
        # Filter by document types if specified
        if rag_query.document_types:
            filtered_chunks = [
                chunk for chunk in filtered_chunks
                if chunk.document_type in rag_query.document_types
            ]
        
        # Sort by relevance score
        filtered_chunks.sort(key=lambda x: x.relevance_score, reverse=True)
        
        # Limit to max chunks
        return filtered_chunks[:rag_query.max_chunks]
    
    async def _generate_answer(self, rag_query: RAGQuery, chunks: List[KnowledgeChunk]) -> str:
        """Generate answer using retrieved chunks."""
        if not chunks:
            return f"I couldn't find specific information about '{rag_query.question}'. You might want to try rephrasing your question or searching for more general terms."
        
        # Combine context from chunks
        context_parts = []
        for i, chunk in enumerate(chunks):
            context_parts.append(f"Source {i+1} ({chunk.document_type.value}):\n{chunk.content}")
        
        combined_context = "\n\n".join(context_parts)
        
        # Generate answer (simplified - in real implementation, use LLM)
        answer = f"""Based on the available documentation and resources, here's what I found about '{rag_query.question}':

{self._synthesize_answer(rag_query.question, chunks)}

**Sources:**
"""
        
        for i, chunk in enumerate(chunks):
            answer += f"{i+1}. {chunk.title} - {chunk.source_url}\n"
        
        return answer
    
    def _synthesize_answer(self, question: str, chunks: List[KnowledgeChunk]) -> str:
        """Synthesize answer from chunks."""
        # Simple answer synthesis (in real implementation, use LLM)
        if "how to" in question.lower():
            return self._generate_how_to_answer(chunks)
        elif "what is" in question.lower():
            return self._generate_definition_answer(chunks)
        elif "example" in question.lower():
            return self._generate_example_answer(chunks)
        else:
            return self._generate_general_answer(chunks)
    
    def _generate_how_to_answer(self, chunks: List[KnowledgeChunk]) -> str:
        """Generate how-to style answer."""
        steps = []
        for chunk in chunks:
            if chunk.document_type == DocumentType.TUTORIAL:
                steps.append(f"• {chunk.content[:200]}...")
        
        if steps:
            return "Here are the key steps:\n\n" + "\n".join(steps)
        else:
            return "Based on the available information, here's the general approach:\n\n" + chunks[0].content[:500] + "..."
    
    def _generate_definition_answer(self, chunks: List[KnowledgeChunk]) -> str:
        """Generate definition-style answer."""
        if chunks:
            return chunks[0].content[:400] + "..."
        return "No specific definition found."
    
    def _generate_example_answer(self, chunks: List[KnowledgeChunk]) -> str:
        """Generate example-based answer."""
        examples = []
        for chunk in chunks:
            if chunk.document_type == DocumentType.CODE_EXAMPLE:
                examples.append(f"```\n{chunk.content[:300]}\n```")
        
        if examples:
            return "Here are some examples:\n\n" + "\n\n".join(examples)
        else:
            return "Here's relevant information:\n\n" + chunks[0].content[:400] + "..."
    
    def _generate_general_answer(self, chunks: List[KnowledgeChunk]) -> str:
        """Generate general answer."""
        if chunks:
            return chunks[0].content[:500] + "..."
        return "No specific information found."
    
    def _calculate_confidence(self, rag_query: RAGQuery, chunks: List[KnowledgeChunk], answer: str) -> float:
        """Calculate confidence score for the answer."""
        if not chunks:
            return 0.0
        
        # Base confidence on chunk relevance scores
        avg_relevance = sum(chunk.relevance_score for chunk in chunks) / len(chunks)
        
        # Boost confidence based on number of sources
        source_boost = min(0.2, len(chunks) * 0.05)
        
        # Boost confidence for specific document types
        type_boost = 0.0
        for chunk in chunks:
            if chunk.document_type in [DocumentType.DOCUMENTATION, DocumentType.API_REFERENCE]:
                type_boost += 0.1
        
        confidence = min(1.0, avg_relevance + source_boost + type_boost)
        return confidence
    
    def _add_to_knowledge_base(self, chunk: KnowledgeChunk):
        """Add chunk to knowledge base."""
        self.knowledge_base[chunk.id] = chunk
        
        # Maintain size limit
        if len(self.knowledge_base) > self.max_knowledge_base_size:
            # Remove oldest chunk
            oldest_id = min(self.knowledge_base.keys(), 
                          key=lambda k: self.knowledge_base[k].created_at)
            del self.knowledge_base[oldest_id]
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics."""
        doc_types = {}
        for chunk in self.knowledge_base.values():
            doc_type = chunk.document_type.value
            doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
        
        return {
            "total_chunks": len(self.knowledge_base),
            "document_types": doc_types,
            "max_size": self.max_knowledge_base_size
        }
