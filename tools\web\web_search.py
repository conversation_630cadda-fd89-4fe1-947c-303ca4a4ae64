#!/usr/bin/env python3

"""
Web Search Engine for CODY v2.0
Advanced web search with multiple sources and intelligent filtering
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import re

logger = logging.getLogger('CODY.WebSearch')

class SearchSource(Enum):
    """Available search sources."""
    GOOGLE = "google"
    BING = "bing"
    DUCKDUCKGO = "duckduckgo"
    STACKOVERFLOW = "stackoverflow"
    GITHUB = "github"
    DOCUMENTATION = "documentation"

class ContentType(Enum):
    """Types of content."""
    GENERAL = "general"
    CODE = "code"
    DOCUMENTATION = "documentation"
    TUTORIAL = "tutorial"
    REFERENCE = "reference"
    EXAMPLE = "example"

@dataclass
class SearchResult:
    """Represents a search result."""
    title: str
    url: str
    snippet: str
    source: SearchSource
    content_type: ContentType
    relevance_score: float
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SearchQuery:
    """Represents a search query."""
    query: str
    sources: List[SearchSource] = field(default_factory=list)
    content_types: List[ContentType] = field(default_factory=list)
    max_results: int = 10
    language: str = "en"
    filters: Dict[str, Any] = field(default_factory=dict)

class WebSearchEngine:
    """
    Advanced web search engine with multiple sources and intelligent filtering.
    
    Features:
    - Multiple search sources (Google, Bing, DuckDuckGo, etc.)
    - Content type classification
    - Relevance scoring and ranking
    - Duplicate detection and removal
    - Rate limiting and caching
    """
    
    def __init__(self):
        """Initialize the web search engine."""
        self.search_providers = self._initialize_search_providers()
        self.content_classifiers = self._initialize_content_classifiers()
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour
        
        logger.info("Web search engine initialized")
    
    def _initialize_search_providers(self) -> Dict[SearchSource, Dict[str, Any]]:
        """Initialize search provider configurations."""
        return {
            SearchSource.GOOGLE: {
                "base_url": "https://www.googleapis.com/customsearch/v1",
                "enabled": False,  # Requires API key
                "rate_limit": 100,  # requests per day
                "quality_score": 0.9
            },
            SearchSource.BING: {
                "base_url": "https://api.bing.microsoft.com/v7.0/search",
                "enabled": False,  # Requires API key
                "rate_limit": 1000,  # requests per month
                "quality_score": 0.8
            },
            SearchSource.DUCKDUCKGO: {
                "base_url": "https://api.duckduckgo.com/",
                "enabled": True,  # No API key required
                "rate_limit": 1000,  # requests per hour
                "quality_score": 0.7
            },
            SearchSource.STACKOVERFLOW: {
                "base_url": "https://api.stackexchange.com/2.3/search",
                "enabled": True,
                "rate_limit": 300,  # requests per day
                "quality_score": 0.95
            },
            SearchSource.GITHUB: {
                "base_url": "https://api.github.com/search",
                "enabled": True,
                "rate_limit": 60,  # requests per hour
                "quality_score": 0.85
            }
        }
    
    def _initialize_content_classifiers(self) -> Dict[ContentType, List[str]]:
        """Initialize content type classification patterns."""
        return {
            ContentType.CODE: [
                r'\b(code|example|snippet|implementation|function|class|method)\b',
                r'\b(github|gist|codepen|jsfiddle)\b',
                r'\.(py|js|java|cpp|go|rs|php|rb)$'
            ],
            ContentType.DOCUMENTATION: [
                r'\b(documentation|docs|api|reference|manual|guide)\b',
                r'\b(readthedocs|docs\.)\b',
                r'/docs?/'
            ],
            ContentType.TUTORIAL: [
                r'\b(tutorial|how.?to|step.?by.?step|walkthrough|guide)\b',
                r'\b(learn|learning|course|lesson)\b'
            ],
            ContentType.REFERENCE: [
                r'\b(reference|spec|specification|standard|rfc)\b',
                r'\b(mdn|w3schools|cppreference)\b'
            ],
            ContentType.EXAMPLE: [
                r'\b(example|sample|demo|showcase|template)\b',
                r'\b(examples?/|samples?/|demos?/)\b'
            ]
        }
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """
        Perform web search across multiple sources.
        
        Args:
            query: Search query with parameters
            
        Returns:
            List of search results
        """
        # Check cache first
        cache_key = self._generate_cache_key(query)
        if cache_key in self.cache:
            cached_result, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                logger.info(f"Returning cached results for: {query.query}")
                return cached_result
        
        results = []
        
        # Determine sources to search
        sources = query.sources if query.sources else [
            SearchSource.DUCKDUCKGO,
            SearchSource.STACKOVERFLOW,
            SearchSource.GITHUB
        ]
        
        # Search each source
        for source in sources:
            if not self.search_providers[source]["enabled"]:
                continue
            
            try:
                source_results = await self._search_source(source, query)
                results.extend(source_results)
            except Exception as e:
                logger.warning(f"Search failed for {source.value}: {e}")
        
        # Process and rank results
        processed_results = self._process_results(results, query)
        
        # Cache results
        self.cache[cache_key] = (processed_results, time.time())
        
        return processed_results
    
    async def _search_source(self, source: SearchSource, query: SearchQuery) -> List[SearchResult]:
        """Search a specific source."""
        if source == SearchSource.DUCKDUCKGO:
            return await self._search_duckduckgo(query)
        elif source == SearchSource.STACKOVERFLOW:
            return await self._search_stackoverflow(query)
        elif source == SearchSource.GITHUB:
            return await self._search_github(query)
        else:
            # Placeholder for other sources
            return []
    
    async def _search_duckduckgo(self, query: SearchQuery) -> List[SearchResult]:
        """Search using DuckDuckGo (simulated)."""
        # This would use actual DuckDuckGo API
        # For now, return simulated results
        results = []
        
        # Simulate some results based on query
        if "react" in query.query.lower():
            results.append(SearchResult(
                title="React Documentation - Getting Started",
                url="https://reactjs.org/docs/getting-started.html",
                snippet="React is a JavaScript library for building user interfaces. Learn what React is all about on our homepage or in the tutorial.",
                source=SearchSource.DUCKDUCKGO,
                content_type=ContentType.DOCUMENTATION,
                relevance_score=0.9
            ))
        
        if "python" in query.query.lower():
            results.append(SearchResult(
                title="Python.org Official Documentation",
                url="https://docs.python.org/3/",
                snippet="The official Python documentation with tutorials, library reference, and language reference.",
                source=SearchSource.DUCKDUCKGO,
                content_type=ContentType.DOCUMENTATION,
                relevance_score=0.95
            ))
        
        return results
    
    async def _search_stackoverflow(self, query: SearchQuery) -> List[SearchResult]:
        """Search Stack Overflow (simulated)."""
        results = []
        
        # Simulate Stack Overflow results
        if any(term in query.query.lower() for term in ["error", "debug", "fix", "problem"]):
            results.append(SearchResult(
                title="How to debug common programming errors",
                url="https://stackoverflow.com/questions/12345/debug-errors",
                snippet="Here are the most effective ways to debug programming errors...",
                source=SearchSource.STACKOVERFLOW,
                content_type=ContentType.CODE,
                relevance_score=0.85
            ))
        
        return results
    
    async def _search_github(self, query: SearchQuery) -> List[SearchResult]:
        """Search GitHub (simulated)."""
        results = []
        
        # Simulate GitHub results
        if "example" in query.query.lower() or "template" in query.query.lower():
            results.append(SearchResult(
                title="Awesome Examples Repository",
                url="https://github.com/awesome/examples",
                snippet="A curated list of awesome examples and templates for various technologies.",
                source=SearchSource.GITHUB,
                content_type=ContentType.EXAMPLE,
                relevance_score=0.8
            ))
        
        return results
    
    def _process_results(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """Process and rank search results."""
        # Remove duplicates
        unique_results = self._remove_duplicates(results)
        
        # Classify content types
        for result in unique_results:
            if result.content_type == ContentType.GENERAL:
                result.content_type = self._classify_content_type(result)
        
        # Calculate relevance scores
        for result in unique_results:
            result.relevance_score = self._calculate_relevance(result, query)
        
        # Sort by relevance
        unique_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        # Apply filters
        filtered_results = self._apply_filters(unique_results, query)
        
        # Limit results
        return filtered_results[:query.max_results]
    
    def _remove_duplicates(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results based on URL."""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        return unique_results
    
    def _classify_content_type(self, result: SearchResult) -> ContentType:
        """Classify content type based on title, URL, and snippet."""
        text = f"{result.title} {result.url} {result.snippet}".lower()
        
        scores = {}
        for content_type, patterns in self.content_classifiers.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text, re.IGNORECASE))
                score += matches
            scores[content_type] = score
        
        # Return the content type with highest score
        if scores:
            best_type = max(scores, key=scores.get)
            if scores[best_type] > 0:
                return best_type
        
        return ContentType.GENERAL
    
    def _calculate_relevance(self, result: SearchResult, query: SearchQuery) -> float:
        """Calculate relevance score for a result."""
        base_score = result.relevance_score
        
        # Boost score based on source quality
        source_quality = self.search_providers[result.source]["quality_score"]
        base_score *= source_quality
        
        # Boost score based on content type preference
        if query.content_types and result.content_type in query.content_types:
            base_score *= 1.2
        
        # Boost score based on query term matches
        query_terms = query.query.lower().split()
        text = f"{result.title} {result.snippet}".lower()
        
        matches = sum(1 for term in query_terms if term in text)
        if query_terms:
            term_score = matches / len(query_terms)
            base_score *= (1 + term_score)
        
        return min(1.0, base_score)
    
    def _apply_filters(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """Apply filters to search results."""
        filtered_results = results
        
        # Filter by content type
        if query.content_types:
            filtered_results = [
                r for r in filtered_results 
                if r.content_type in query.content_types
            ]
        
        # Apply custom filters
        for filter_name, filter_value in query.filters.items():
            if filter_name == "min_relevance":
                filtered_results = [
                    r for r in filtered_results 
                    if r.relevance_score >= filter_value
                ]
            elif filter_name == "exclude_domains":
                excluded_domains = filter_value if isinstance(filter_value, list) else [filter_value]
                filtered_results = [
                    r for r in filtered_results 
                    if not any(domain in r.url for domain in excluded_domains)
                ]
        
        return filtered_results
    
    def _generate_cache_key(self, query: SearchQuery) -> str:
        """Generate cache key for a query."""
        key_data = {
            "query": query.query,
            "sources": [s.value for s in query.sources],
            "content_types": [ct.value for ct in query.content_types],
            "max_results": query.max_results,
            "language": query.language
        }
        return str(hash(json.dumps(key_data, sort_keys=True)))
    
    async def search_documentation(self, technology: str, topic: str = "") -> List[SearchResult]:
        """Search for documentation on a specific technology."""
        query_text = f"{technology} documentation {topic}".strip()
        
        query = SearchQuery(
            query=query_text,
            content_types=[ContentType.DOCUMENTATION, ContentType.REFERENCE],
            max_results=5
        )
        
        return await self.search(query)
    
    async def search_examples(self, technology: str, use_case: str = "") -> List[SearchResult]:
        """Search for code examples."""
        query_text = f"{technology} example {use_case}".strip()
        
        query = SearchQuery(
            query=query_text,
            sources=[SearchSource.GITHUB, SearchSource.STACKOVERFLOW],
            content_types=[ContentType.CODE, ContentType.EXAMPLE],
            max_results=5
        )
        
        return await self.search(query)
    
    async def search_tutorials(self, topic: str) -> List[SearchResult]:
        """Search for tutorials on a topic."""
        query_text = f"{topic} tutorial how to"
        
        query = SearchQuery(
            query=query_text,
            content_types=[ContentType.TUTORIAL],
            max_results=5
        )
        
        return await self.search(query)
    
    def get_search_suggestions(self, partial_query: str) -> List[str]:
        """Get search suggestions based on partial query."""
        suggestions = []
        
        # Common programming-related suggestions
        if "react" in partial_query.lower():
            suggestions.extend([
                "react hooks tutorial",
                "react best practices",
                "react component examples",
                "react state management"
            ])
        elif "python" in partial_query.lower():
            suggestions.extend([
                "python tutorial",
                "python best practices",
                "python examples",
                "python debugging"
            ])
        elif "javascript" in partial_query.lower():
            suggestions.extend([
                "javascript tutorial",
                "javascript examples",
                "javascript async await",
                "javascript best practices"
            ])
        
        return suggestions[:5]
