#!/usr/bin/env python3

"""
Multi-Terminal Manager for CODY v2.0
Advanced terminal integration with multi-session support
"""

import asyncio
import logging
import subprocess
import threading
import time
import uuid
import shutil
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import os
import platform

logger = logging.getLogger('CODY.MultiTerminal')

class TerminalType(Enum):
    """Types of terminal sessions."""
    BASH = "bash"
    POWERSHELL = "powershell"
    CMD = "cmd"
    ZSH = "zsh"
    FISH = "fish"
    PYTHON = "python"
    NODE = "node"

class SessionStatus(Enum):
    """Terminal session status."""
    ACTIVE = "active"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    CLOSED = "closed"

@dataclass
class CommandResult:
    """Result of a command execution."""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    session_id: str
    timestamp: float = field(default_factory=time.time)

@dataclass
class TerminalSession:
    """Represents a terminal session."""
    session_id: str
    terminal_type: TerminalType
    working_directory: str
    environment: Dict[str, str]
    status: SessionStatus
    process: Optional[subprocess.Popen] = None
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    command_history: List[CommandResult] = field(default_factory=list)

class MultiTerminalManager:
    """
    Advanced terminal manager supporting multiple concurrent sessions.
    
    Features:
    - Multiple terminal types (bash, powershell, cmd, etc.)
    - Session management and isolation
    - Command execution with timeout
    - Output streaming and capture
    - Environment variable management
    - Command history and suggestions
    """
    
    def __init__(self, max_sessions: int = 10):
        """Initialize the multi-terminal manager."""
        self.max_sessions = max_sessions
        self.sessions: Dict[str, TerminalSession] = {}
        self.default_timeout = 30.0
        self.command_suggestions: Dict[str, List[str]] = {}
        
        # Detect system shell
        self.system_shell = self._detect_system_shell()
        
        logger.info(f"Multi-terminal manager initialized with {self.system_shell} as default shell")
    
    def _detect_system_shell(self) -> TerminalType:
        """Detect the system's default shell."""
        system = platform.system().lower()
        
        if system == "windows":
            return TerminalType.POWERSHELL
        elif system in ["linux", "darwin"]:
            # Check for available shells
            if shutil.which("zsh"):
                return TerminalType.ZSH
            elif shutil.which("bash"):
                return TerminalType.BASH
            else:
                return TerminalType.BASH
        else:
            return TerminalType.BASH
    
    async def create_session(self, 
                           terminal_type: Optional[TerminalType] = None,
                           working_directory: Optional[str] = None,
                           environment: Optional[Dict[str, str]] = None) -> str:
        """
        Create a new terminal session.
        
        Args:
            terminal_type: Type of terminal to create
            working_directory: Initial working directory
            environment: Environment variables
            
        Returns:
            Session ID
        """
        if len(self.sessions) >= self.max_sessions:
            raise Exception(f"Maximum number of sessions ({self.max_sessions}) reached")
        
        session_id = str(uuid.uuid4())
        terminal_type = terminal_type or self.system_shell
        working_directory = working_directory or os.getcwd()
        environment = environment or os.environ.copy()
        
        try:
            # Create the session
            session = TerminalSession(
                session_id=session_id,
                terminal_type=terminal_type,
                working_directory=working_directory,
                environment=environment,
                status=SessionStatus.IDLE
            )
            
            # Start the terminal process
            await self._start_terminal_process(session)
            
            self.sessions[session_id] = session
            logger.info(f"Created terminal session {session_id} ({terminal_type.value})")
            
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create terminal session: {e}")
            raise
    
    async def _start_terminal_process(self, session: TerminalSession):
        """Start the terminal process for a session."""
        try:
            if session.terminal_type == TerminalType.BASH:
                cmd = ["bash", "-i"]
            elif session.terminal_type == TerminalType.ZSH:
                cmd = ["zsh", "-i"]
            elif session.terminal_type == TerminalType.POWERSHELL:
                cmd = ["powershell", "-NoExit", "-Command", "-"]
            elif session.terminal_type == TerminalType.CMD:
                cmd = ["cmd", "/k"]
            elif session.terminal_type == TerminalType.PYTHON:
                cmd = ["python", "-i"]
            elif session.terminal_type == TerminalType.NODE:
                cmd = ["node"]
            else:
                cmd = ["bash", "-i"]
            
            # Start process
            session.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=session.working_directory,
                env=session.environment,
                text=True,
                bufsize=0
            )
            
            session.status = SessionStatus.ACTIVE
            
        except Exception as e:
            session.status = SessionStatus.ERROR
            logger.error(f"Failed to start terminal process: {e}")
            raise
    
    async def execute_command(self, 
                            session_id: str,
                            command: str,
                            timeout: Optional[float] = None) -> CommandResult:
        """
        Execute a command in a terminal session.
        
        Args:
            session_id: Session ID
            command: Command to execute
            timeout: Command timeout in seconds
            
        Returns:
            Command result
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")

        session = self.sessions[session_id]
        timeout = timeout or self.default_timeout

        if session.status != SessionStatus.ACTIVE:
            raise Exception(f"Session {session_id} is not active")

        start_time = time.time()
        session.status = SessionStatus.BUSY

        try:
            logger.info(f"Executing command '{command}' in session {session_id}.")
            # Execute command
            if session.terminal_type in [TerminalType.PYTHON, TerminalType.NODE]:
                # For interactive interpreters, handle differently
                result = await self._execute_interpreter_command(session, command, timeout)
            else:
                # For shell commands
                result = await self._execute_shell_command(session, command, timeout)

            # Update session
            session.last_activity = time.time()
            session.command_history.append(result)
            session.status = SessionStatus.ACTIVE

            # Update command suggestions
            self._update_command_suggestions(command, result)

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            session.status = SessionStatus.ERROR

            error_result = CommandResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=execution_time,
                session_id=session_id
            )

            session.command_history.append(error_result)
            logger.error(f"Error executing command '{command}' in session {session_id}: {e}", exc_info=True)
            return error_result
    
    async def _execute_shell_command(self, 
                                   session: TerminalSession,
                                   command: str,
                                   timeout: float) -> CommandResult:
        """Execute a shell command."""
        start_time = time.time()
        
        try:
            logger.info(f"Sending command '{command}' to process in session {session.session_id}")
            # Send command to process
            session.process.stdin.write(f"{command}\n")
            await session.process.stdin.drain()

            # Read output with timeout
            stdout_lines = []
            stderr_lines = []

            # Simple timeout implementation
            end_time = start_time + timeout

            while time.time() < end_time:
                # Check if process has output
                if session.process.poll() is not None:
                    break

                # Read available output
                try:
                    # This is a simplified implementation
                    # In practice, you'd use select() or asyncio for non-blocking reads
                    await asyncio.sleep(0.1)
                except:
                    break

            execution_time = time.time() - start_time

            return CommandResult(
                command=command,
                exit_code=0,  # Simplified
                stdout="\n".join(stdout_lines),
                stderr="\n".join(stderr_lines),
                execution_time=execution_time,
                session_id=session.session_id
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Command execution failed in session {session.session_id}: {e}", exc_info=True)
            raise Exception(f"Command execution failed: {e}")
    
    async def _execute_interpreter_command(self, 
                                         session: TerminalSession,
                                         command: str,
                                         timeout: float) -> CommandResult:
        """Execute a command in an interpreter (Python/Node)."""
        start_time = time.time()
        
        try:
            # For interpreters, we need special handling
            # This is a simplified implementation
            
            if session.terminal_type == TerminalType.PYTHON:
                # Execute Python code
                result = await self._execute_python_code(command, timeout)
            elif session.terminal_type == TerminalType.NODE:
                # Execute JavaScript code
                result = await self._execute_node_code(command, timeout)
            else:
                raise Exception(f"Unsupported interpreter: {session.terminal_type}")
            
            execution_time = time.time() - start_time
            
            return CommandResult(
                command=command,
                exit_code=0,
                stdout=result,
                stderr="",
                execution_time=execution_time,
                session_id=session.session_id
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            raise Exception(f"Interpreter command failed: {e}")
    
    async def _execute_python_code(self, code: str, timeout: float) -> str:
        """Execute Python code safely."""
        # This would use a proper Python execution environment
        # For now, return a placeholder
        return f"Python execution result for: {code}"
    
    async def _execute_node_code(self, code: str, timeout: float) -> str:
        """Execute Node.js code safely."""
        # This would use a proper Node.js execution environment
        # For now, return a placeholder
        return f"Node.js execution result for: {code}"
    
    def _update_command_suggestions(self, command: str, result: CommandResult):
        """Update command suggestions based on execution history."""
        # Extract command base (first word)
        base_command = command.split()[0] if command.split() else ""
        
        if base_command not in self.command_suggestions:
            self.command_suggestions[base_command] = []
        
        # Add successful command variations
        if result.exit_code == 0 and command not in self.command_suggestions[base_command]:
            self.command_suggestions[base_command].append(command)
            
            # Keep only recent suggestions
            if len(self.command_suggestions[base_command]) > 10:
                self.command_suggestions[base_command] = self.command_suggestions[base_command][-10:]
    
    def get_command_suggestions(self, partial_command: str) -> List[str]:
        """Get command suggestions based on history."""
        suggestions = []
        
        for base_cmd, commands in self.command_suggestions.items():
            if base_cmd.startswith(partial_command):
                suggestions.extend(commands)
        
        return suggestions[:10]  # Return top 10 suggestions
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a session."""
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        
        return {
            "session_id": session.session_id,
            "terminal_type": session.terminal_type.value,
            "working_directory": session.working_directory,
            "status": session.status.value,
            "created_at": session.created_at,
            "last_activity": session.last_activity,
            "command_count": len(session.command_history),
            "is_alive": session.process and session.process.poll() is None
        }
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """List all active sessions."""
        sessions_info = []
        
        for session_id, session in self.sessions.items():
            info = {
                "session_id": session_id,
                "terminal_type": session.terminal_type.value,
                "status": session.status.value,
                "working_directory": session.working_directory,
                "created_at": session.created_at,
                "last_activity": session.last_activity,
                "command_count": len(session.command_history)
            }
            sessions_info.append(info)
        
        return sessions_info
    
    async def close_session(self, session_id: str) -> bool:
        """Close a terminal session."""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        try:
            # Terminate the process
            if session.process:
                session.process.terminate()
                
                # Wait for process to terminate
                try:
                    session.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    session.process.kill()
            
            session.status = SessionStatus.CLOSED
            del self.sessions[session_id]
            
            logger.info(f"Closed terminal session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error closing session {session_id}: {e}")
            return False
    
    async def change_directory(self, session_id: str, directory: str) -> bool:
        """Change working directory for a session."""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        try:
            # Execute cd command
            if session.terminal_type == TerminalType.POWERSHELL:
                command = f"Set-Location '{directory}'"
            else:
                command = f"cd '{directory}'"
            
            result = await self.execute_command(session_id, command)
            
            if result.exit_code == 0:
                session.working_directory = directory
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error changing directory: {e}")
            return False
    
    def get_session_history(self, session_id: str, limit: int = 50) -> List[CommandResult]:
        """Get command history for a session."""
        if session_id not in self.sessions:
            return []
        
        session = self.sessions[session_id]
        return session.command_history[-limit:]
    
    async def cleanup_inactive_sessions(self, max_idle_time: float = 3600):
        """Clean up sessions that have been idle for too long."""
        current_time = time.time()
        sessions_to_close = []
        
        for session_id, session in self.sessions.items():
            if current_time - session.last_activity > max_idle_time:
                sessions_to_close.append(session_id)
        
        for session_id in sessions_to_close:
            await self.close_session(session_id)
            logger.info(f"Cleaned up inactive session {session_id}")
    
    def shutdown(self):
        """Shutdown all terminal sessions."""
        for session_id in list(self.sessions.keys()):
            asyncio.create_task(self.close_session(session_id))
        
        logger.info("Multi-terminal manager shutdown complete")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
