#!/usr/bin/env python3

"""
AI Provider Manager for CODY v2.0
Manages multiple AI providers with dynamic switching and load balancing
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import threading

from .base_provider import (
    BaseAIProvider, 
    ProviderConfig, 
    ModelInfo, 
    ChatRequest, 
    ChatResponse,
    ProviderStatus
)
from .deepseek_provider import DeepSeekProvider
from .gemini_provider import GeminiProvider

logger = logging.getLogger('CODY.ProviderManager')

class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    FASTEST_RESPONSE = "fastest_response"
    COST_OPTIMIZED = "cost_optimized"

@dataclass
class ProviderHealth:
    """Health status of a provider."""
    provider_name: str
    is_healthy: bool
    last_check: float
    response_time: float
    error_rate: float
    uptime_percentage: float

class ProviderManager:
    """
    Manages multiple AI providers with dynamic switching capabilities.
    
    Features:
    - Dynamic model switching with /switch command
    - Automatic failover between providers
    - Load balancing across multiple providers
    - Health monitoring and circuit breaker pattern
    - Cost optimization and usage tracking
    """
    
    def __init__(self, 
                 default_provider: str = "deepseek",
                 enable_failover: bool = True,
                 enable_load_balancing: bool = False,
                 health_check_interval: float = 60.0):
        """
        Initialize the provider manager.
        
        Args:
            default_provider: Default provider to use
            enable_failover: Enable automatic failover
            enable_load_balancing: Enable load balancing
            health_check_interval: Health check interval in seconds
        """
        self.default_provider = default_provider
        self.enable_failover = enable_failover
        self.enable_load_balancing = enable_load_balancing
        self.health_check_interval = health_check_interval
        
        # Provider management
        self.providers: Dict[str, BaseAIProvider] = {}
        self.provider_health: Dict[str, ProviderHealth] = {}
        self.current_provider = default_provider
        
        # Load balancing
        self.load_balancing_strategy = LoadBalancingStrategy.ROUND_ROBIN
        self.round_robin_index = 0
        
        # Threading
        self.lock = threading.RLock()
        self.health_check_thread = None
        self.shutdown_event = threading.Event()
        
        # Initialize providers
        self._initialize_providers()
        
        # Start health monitoring
        if self.enable_failover:
            self._start_health_monitoring()
    
    def _initialize_providers(self) -> None:
        """Initialize all available providers."""
        try:
            # Initialize DeepSeek provider
            try:
                deepseek_provider = DeepSeekProvider()
                self.providers["deepseek"] = deepseek_provider
                self.provider_health["deepseek"] = ProviderHealth(
                    provider_name="deepseek",
                    is_healthy=deepseek_provider.is_available(),
                    last_check=time.time(),
                    response_time=0.0,
                    error_rate=0.0,
                    uptime_percentage=100.0
                )
                logger.info("DeepSeek provider initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize DeepSeek provider: {e}")
            
            # Initialize Gemini provider
            try:
                gemini_provider = GeminiProvider()
                self.providers["gemini"] = gemini_provider
                self.provider_health["gemini"] = ProviderHealth(
                    provider_name="gemini",
                    is_healthy=gemini_provider.is_available(),
                    last_check=time.time(),
                    response_time=0.0,
                    error_rate=0.0,
                    uptime_percentage=100.0
                )
                logger.info("Gemini provider initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Gemini provider: {e}")
            
            if not self.providers:
                raise Exception("No providers could be initialized")
            
            # Set current provider to first available if default is not available
            if self.current_provider not in self.providers:
                self.current_provider = next(iter(self.providers.keys()))
            
            logger.info(f"Provider manager initialized with {len(self.providers)} providers")
            
        except Exception as e:
            logger.error(f"Failed to initialize provider manager: {e}")
            raise
    
    def _start_health_monitoring(self) -> None:
        """Start background health monitoring."""
        self.health_check_thread = threading.Thread(
            target=self._health_check_loop,
            daemon=True,
            name="ProviderHealthMonitor"
        )
        self.health_check_thread.start()
        logger.info("Health monitoring started")
    
    def _health_check_loop(self) -> None:
        """Background health check loop."""
        while not self.shutdown_event.is_set():
            try:
                self._check_provider_health()
                self.shutdown_event.wait(self.health_check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
    
    def _check_provider_health(self) -> None:
        """Check health of all providers."""
        for provider_name, provider in self.providers.items():
            try:
                start_time = time.time()
                is_healthy = provider.is_available()
                response_time = time.time() - start_time
                
                metrics = provider.get_metrics()
                error_rate = 0.0
                if metrics.total_requests > 0:
                    error_rate = metrics.failed_requests / metrics.total_requests * 100
                
                with self.lock:
                    self.provider_health[provider_name] = ProviderHealth(
                        provider_name=provider_name,
                        is_healthy=is_healthy,
                        last_check=time.time(),
                        response_time=response_time,
                        error_rate=error_rate,
                        uptime_percentage=metrics.uptime_percentage
                    )
                
            except Exception as e:
                logger.warning(f"Health check failed for {provider_name}: {e}")
                with self.lock:
                    if provider_name in self.provider_health:
                        self.provider_health[provider_name].is_healthy = False
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion using the current provider.
        
        Args:
            request: Chat completion request
            
        Returns:
            Chat completion response
        """
        provider = self._get_provider_for_request(request)
        
        if not provider:
            raise Exception("No healthy providers available")
        
        try:
            return await provider.chat_completion(request)
        except Exception as e:
            if self.enable_failover:
                # Try failover to another provider
                return await self._failover_chat_completion(request, provider.provider_name)
            raise
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion using the current provider.
        
        Args:
            request: Chat completion request
            
        Yields:
            Partial response chunks
        """
        provider = self._get_provider_for_request(request)
        
        if not provider:
            raise Exception("No healthy providers available")
        
        try:
            async for chunk in provider.stream_chat_completion(request):
                yield chunk
        except Exception as e:
            if self.enable_failover:
                # Try failover to another provider
                async for chunk in self._failover_stream_chat_completion(request, provider.provider_name):
                    yield chunk
            else:
                raise
    
    def _get_provider_for_request(self, request: ChatRequest) -> Optional[BaseAIProvider]:
        """Get the best provider for a request."""
        if self.enable_load_balancing:
            return self._get_load_balanced_provider(request)
        else:
            return self._get_current_provider()
    
    def _get_current_provider(self) -> Optional[BaseAIProvider]:
        """Get the current active provider."""
        with self.lock:
            provider = self.providers.get(self.current_provider)
            if provider and self._is_provider_healthy(self.current_provider):
                return provider
            
            # Current provider is unhealthy, find a healthy one
            for name, provider in self.providers.items():
                if self._is_provider_healthy(name):
                    self.current_provider = name
                    logger.info(f"Switched to healthy provider: {name}")
                    return provider
            
            return None
    
    def _get_load_balanced_provider(self, request: ChatRequest) -> Optional[BaseAIProvider]:
        """Get provider using load balancing strategy."""
        healthy_providers = [
            (name, provider) for name, provider in self.providers.items()
            if self._is_provider_healthy(name)
        ]
        
        if not healthy_providers:
            return None
        
        if self.load_balancing_strategy == LoadBalancingStrategy.ROUND_ROBIN:
            with self.lock:
                provider_name, provider = healthy_providers[self.round_robin_index % len(healthy_providers)]
                self.round_robin_index += 1
                return provider
        
        elif self.load_balancing_strategy == LoadBalancingStrategy.FASTEST_RESPONSE:
            # Choose provider with fastest average response time
            fastest_provider = min(
                healthy_providers,
                key=lambda x: self.provider_health[x[0]].response_time
            )
            return fastest_provider[1]
        
        elif self.load_balancing_strategy == LoadBalancingStrategy.LEAST_LOADED:
            # Choose provider with least active requests (simplified)
            least_loaded = min(
                healthy_providers,
                key=lambda x: len(x[1].active_tasks) if hasattr(x[1], 'active_tasks') else 0
            )
            return least_loaded[1]
        
        else:
            # Default to first healthy provider
            return healthy_providers[0][1]
    
    def _is_provider_healthy(self, provider_name: str) -> bool:
        """Check if a provider is healthy."""
        with self.lock:
            health = self.provider_health.get(provider_name)
            if not health:
                return False
            
            # Consider provider healthy if:
            # 1. It's marked as healthy
            # 2. Error rate is below 50%
            # 3. Last check was recent (within 2x health check interval)
            current_time = time.time()
            is_recent = (current_time - health.last_check) < (self.health_check_interval * 2)
            
            return health.is_healthy and health.error_rate < 50.0 and is_recent
    
    async def _failover_chat_completion(self, request: ChatRequest, failed_provider: str) -> ChatResponse:
        """Attempt failover for chat completion."""
        logger.warning(f"Attempting failover from {failed_provider}")
        
        for name, provider in self.providers.items():
            if name != failed_provider and self._is_provider_healthy(name):
                try:
                    logger.info(f"Failing over to provider: {name}")
                    return await provider.chat_completion(request)
                except Exception as e:
                    logger.warning(f"Failover to {name} also failed: {e}")
                    continue
        
        raise Exception("All providers failed")
    
    async def _failover_stream_chat_completion(self, request: ChatRequest, failed_provider: str) -> AsyncGenerator[str, None]:
        """Attempt failover for streaming chat completion."""
        logger.warning(f"Attempting streaming failover from {failed_provider}")
        
        for name, provider in self.providers.items():
            if name != failed_provider and self._is_provider_healthy(name):
                try:
                    logger.info(f"Failing over to provider: {name}")
                    async for chunk in provider.stream_chat_completion(request):
                        yield chunk
                    return
                except Exception as e:
                    logger.warning(f"Streaming failover to {name} also failed: {e}")
                    continue
        
        raise Exception("All providers failed for streaming")
    
    def switch_provider(self, provider_name: str) -> bool:
        """
        Switch to a different provider.
        
        Args:
            provider_name: Name of the provider to switch to
            
        Returns:
            True if switch was successful, False otherwise
        """
        with self.lock:
            if provider_name not in self.providers:
                logger.warning(f"Unknown provider: {provider_name}")
                return False
            
            if not self._is_provider_healthy(provider_name):
                logger.warning(f"Provider {provider_name} is not healthy")
                return False
            
            old_provider = self.current_provider
            self.current_provider = provider_name
            logger.info(f"Switched provider from {old_provider} to {provider_name}")
            return True
    
    def get_available_models(self) -> Dict[str, List[ModelInfo]]:
        """Get all available models from all providers."""
        models = {}
        for name, provider in self.providers.items():
            try:
                models[name] = provider.get_available_models()
            except Exception as e:
                logger.warning(f"Failed to get models from {name}: {e}")
                models[name] = []
        return models
    
    def get_provider_status(self) -> Dict[str, Dict]:
        """Get status of all providers."""
        status = {}
        with self.lock:
            for name, provider in self.providers.items():
                try:
                    provider_status = provider.get_status()
                    health = self.provider_health.get(name)
                    if health:
                        provider_status["health"] = health.__dict__
                    status[name] = provider_status
                except Exception as e:
                    status[name] = {"error": str(e)}
        return status
    
    def get_current_provider_name(self) -> str:
        """Get the name of the current provider."""
        return self.current_provider
    
    def shutdown(self) -> None:
        """Shutdown the provider manager."""
        logger.info("Shutting down provider manager")
        self.shutdown_event.set()
        
        if self.health_check_thread:
            self.health_check_thread.join(timeout=5.0)
        
        for provider in self.providers.values():
            try:
                if hasattr(provider, 'shutdown'):
                    provider.shutdown()
            except Exception as e:
                logger.warning(f"Error shutting down provider: {e}")
        
        logger.info("Provider manager shutdown complete")
