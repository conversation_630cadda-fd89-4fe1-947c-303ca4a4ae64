#!/usr/bin/env python3

"""
Intent Analyzer for CODY v2.0
Advanced intent recognition and action planning
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

from .advanced_nlp import IntentCategory, Intent, Entity, Complexity

logger = logging.getLogger('CODY.IntentAnalyzer')

class ActionType(Enum):
    """Types of actions that can be performed."""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"
    ANALYZE = "analyze"
    SEARCH = "search"
    CONFIGURE = "configure"
    HELP = "help"

class Priority(Enum):
    """Action priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ActionPlan:
    """Represents a planned action."""
    action_type: ActionType
    target: str
    parameters: Dict[str, Any]
    priority: Priority
    estimated_duration: float
    dependencies: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    success_criteria: List[str] = field(default_factory=list)

@dataclass
class WorkflowStep:
    """Represents a step in a workflow."""
    step_id: str
    description: str
    action_plan: ActionPlan
    prerequisites: List[str] = field(default_factory=list)
    outputs: List[str] = field(default_factory=list)
    validation_rules: List[str] = field(default_factory=list)

@dataclass
class IntentAnalysisResult:
    """Result of intent analysis."""
    original_intent: Intent
    refined_intent: Intent
    action_plans: List[ActionPlan]
    workflow_steps: List[WorkflowStep]
    confidence_score: float
    risk_assessment: str
    estimated_total_time: float
    requires_user_confirmation: bool
    metadata: Dict[str, Any] = field(default_factory=dict)

class IntentAnalyzer:
    """
    Advanced intent analyzer that converts natural language intents
    into actionable plans and workflows.
    """
    
    def __init__(self):
        """Initialize the intent analyzer."""
        self.action_templates = self._initialize_action_templates()
        self.workflow_patterns = self._initialize_workflow_patterns()
        self.risk_patterns = self._initialize_risk_patterns()
        
        logger.info("Intent analyzer initialized")
    
    def _initialize_action_templates(self) -> Dict[IntentCategory, Dict[str, Any]]:
        """Initialize action templates for different intent categories."""
        return {
            IntentCategory.CODE_GENERATION: {
                "primary_action": ActionType.CREATE,
                "common_targets": ["file", "project", "component", "function", "class"],
                "parameters": {
                    "language": "auto-detect",
                    "framework": "auto-detect",
                    "style": "best-practices",
                    "testing": True,
                    "documentation": True
                },
                "workflow_template": [
                    "analyze_requirements",
                    "setup_project_structure",
                    "generate_core_code",
                    "add_tests",
                    "add_documentation",
                    "validate_output"
                ]
            },
            IntentCategory.CODE_DEBUGGING: {
                "primary_action": ActionType.ANALYZE,
                "common_targets": ["error", "bug", "issue", "performance"],
                "parameters": {
                    "analysis_depth": "comprehensive",
                    "suggest_fixes": True,
                    "run_tests": True,
                    "check_dependencies": True
                },
                "workflow_template": [
                    "identify_issue",
                    "analyze_code",
                    "reproduce_error",
                    "generate_fix",
                    "test_fix",
                    "validate_solution"
                ]
            },
            IntentCategory.FILE_OPERATIONS: {
                "primary_action": ActionType.EXECUTE,
                "common_targets": ["file", "directory", "path"],
                "parameters": {
                    "backup": True,
                    "validate_permissions": True,
                    "confirm_destructive": True
                },
                "workflow_template": [
                    "validate_path",
                    "check_permissions",
                    "backup_if_needed",
                    "execute_operation",
                    "verify_result"
                ]
            },
            IntentCategory.TERMINAL_COMMANDS: {
                "primary_action": ActionType.EXECUTE,
                "common_targets": ["command", "script", "process"],
                "parameters": {
                    "environment": "auto-detect",
                    "safety_check": True,
                    "capture_output": True,
                    "timeout": 300
                },
                "workflow_template": [
                    "validate_command",
                    "check_environment",
                    "execute_safely",
                    "capture_output",
                    "analyze_result"
                ]
            },
            IntentCategory.WEB_SEARCH: {
                "primary_action": ActionType.SEARCH,
                "common_targets": ["documentation", "tutorial", "example", "solution"],
                "parameters": {
                    "sources": ["official_docs", "stackoverflow", "github"],
                    "relevance_threshold": 0.7,
                    "max_results": 10
                },
                "workflow_template": [
                    "formulate_query",
                    "search_sources",
                    "filter_results",
                    "summarize_findings",
                    "present_results"
                ]
            },
            IntentCategory.PROJECT_MANAGEMENT: {
                "primary_action": ActionType.CREATE,
                "common_targets": ["project", "structure", "configuration"],
                "parameters": {
                    "template": "auto-select",
                    "best_practices": True,
                    "git_init": True,
                    "ci_cd": "optional"
                },
                "workflow_template": [
                    "analyze_requirements",
                    "select_template",
                    "create_structure",
                    "configure_tools",
                    "initialize_git",
                    "setup_ci_cd"
                ]
            }
        }
    
    def _initialize_workflow_patterns(self) -> Dict[str, List[str]]:
        """Initialize common workflow patterns."""
        return {
            "full_stack_app": [
                "setup_backend",
                "setup_frontend", 
                "setup_database",
                "configure_api",
                "add_authentication",
                "add_testing",
                "setup_deployment"
            ],
            "api_development": [
                "design_endpoints",
                "setup_routing",
                "add_middleware",
                "implement_handlers",
                "add_validation",
                "add_documentation",
                "add_tests"
            ],
            "data_analysis": [
                "load_data",
                "clean_data",
                "explore_data",
                "analyze_patterns",
                "create_visualizations",
                "generate_insights",
                "create_report"
            ],
            "machine_learning": [
                "prepare_data",
                "feature_engineering",
                "select_model",
                "train_model",
                "evaluate_model",
                "tune_hyperparameters",
                "deploy_model"
            ]
        }
    
    def _initialize_risk_patterns(self) -> Dict[str, List[str]]:
        """Initialize risk assessment patterns."""
        return {
            "high_risk": [
                r'\b(delete|remove|drop|truncate|format)\b',
                r'\b(sudo|admin|root|system)\b',
                r'\b(production|prod|live)\b',
                r'\b(database|db|table)\b.*\b(drop|delete)\b'
            ],
            "medium_risk": [
                r'\b(modify|change|update|alter)\b',
                r'\b(install|uninstall|upgrade)\b',
                r'\b(config|configuration|settings)\b',
                r'\b(network|firewall|security)\b'
            ],
            "low_risk": [
                r'\b(read|view|show|display|list)\b',
                r'\b(create|add|new)\b',
                r'\b(help|info|status)\b'
            ]
        }
    
    async def analyze_intent(self, intent: Intent, context: Optional[Dict[str, Any]] = None) -> IntentAnalysisResult:
        """
        Analyze intent and create actionable plans.
        
        Args:
            intent: The intent to analyze
            context: Optional context information
            
        Returns:
            Detailed analysis result with action plans
        """
        try:
            # Refine the intent based on context
            refined_intent = self._refine_intent(intent, context)
            
            # Generate action plans
            action_plans = self._generate_action_plans(refined_intent, context)
            
            # Create workflow steps
            workflow_steps = self._create_workflow_steps(action_plans, refined_intent)
            
            # Assess risks
            risk_assessment = self._assess_risks(refined_intent, action_plans)
            
            # Calculate confidence and time estimates
            confidence_score = self._calculate_confidence(refined_intent, action_plans)
            estimated_total_time = sum(plan.estimated_duration for plan in action_plans)
            
            # Determine if user confirmation is needed
            requires_confirmation = self._requires_confirmation(refined_intent, action_plans, risk_assessment)
            
            return IntentAnalysisResult(
                original_intent=intent,
                refined_intent=refined_intent,
                action_plans=action_plans,
                workflow_steps=workflow_steps,
                confidence_score=confidence_score,
                risk_assessment=risk_assessment,
                estimated_total_time=estimated_total_time,
                requires_user_confirmation=requires_confirmation,
                metadata={
                    "context_used": context is not None,
                    "workflow_complexity": len(workflow_steps),
                    "risk_level": risk_assessment
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            # Return basic analysis on error
            return IntentAnalysisResult(
                original_intent=intent,
                refined_intent=intent,
                action_plans=[],
                workflow_steps=[],
                confidence_score=0.5,
                risk_assessment="unknown",
                estimated_total_time=60.0,
                requires_user_confirmation=True
            )
    
    def _refine_intent(self, intent: Intent, context: Optional[Dict[str, Any]]) -> Intent:
        """Refine intent based on context and analysis."""
        refined_intent = Intent(
            category=intent.category,
            action=intent.action,
            confidence=intent.confidence,
            entities=intent.entities.copy(),
            complexity=intent.complexity,
            estimated_time=intent.estimated_time,
            requires_confirmation=intent.requires_confirmation,
            metadata=intent.metadata.copy()
        )
        
        # Enhance based on context
        if context:
            # Adjust complexity based on context
            if context.get("user_experience_level") == "beginner":
                if refined_intent.complexity == Complexity.COMPLEX:
                    refined_intent.complexity = Complexity.EXPERT
            
            # Adjust action based on recent history
            if context.get("recent_actions"):
                recent_actions = context["recent_actions"]
                if "create" in recent_actions and intent.action == "create":
                    refined_intent.action = "extend"
        
        # Enhance entities with additional context
        for entity in refined_intent.entities:
            if entity.type == "file_path" and not entity.value.startswith("/") and not ":" in entity.value:
                entity.metadata["relative_path"] = True
        
        return refined_intent
    
    def _generate_action_plans(self, intent: Intent, context: Optional[Dict[str, Any]]) -> List[ActionPlan]:
        """Generate action plans for the intent."""
        action_plans = []
        
        template = self.action_templates.get(intent.category)
        if not template:
            # Create basic action plan
            return [ActionPlan(
                action_type=ActionType.EXECUTE,
                target="general",
                parameters={},
                priority=Priority.NORMAL,
                estimated_duration=intent.estimated_time
            )]
        
        # Create primary action plan
        primary_plan = ActionPlan(
            action_type=template["primary_action"],
            target=self._determine_target(intent, template["common_targets"]),
            parameters=template["parameters"].copy(),
            priority=self._determine_priority(intent),
            estimated_duration=intent.estimated_time
        )
        
        # Customize parameters based on entities
        for entity in intent.entities:
            if entity.type == "programming_language":
                primary_plan.parameters["language"] = entity.value
            elif entity.type == "framework":
                primary_plan.parameters["framework"] = entity.value
            elif entity.type == "file_path":
                primary_plan.parameters["target_path"] = entity.value
        
        # Add success criteria
        primary_plan.success_criteria = self._generate_success_criteria(intent, primary_plan)
        
        # Add dependencies
        primary_plan.dependencies = self._identify_dependencies(intent, primary_plan)
        
        # Add risks
        primary_plan.risks = self._identify_action_risks(intent, primary_plan)
        
        action_plans.append(primary_plan)
        
        # Add supporting action plans if needed
        if intent.complexity in [Complexity.COMPLEX, Complexity.EXPERT]:
            supporting_plans = self._generate_supporting_plans(intent, primary_plan)
            action_plans.extend(supporting_plans)
        
        return action_plans
    
    def _determine_target(self, intent: Intent, common_targets: List[str]) -> str:
        """Determine the target for the action."""
        # Check entities for specific targets
        for entity in intent.entities:
            if entity.type in ["file_path", "function_name", "class_name"]:
                return entity.value
        
        # Use action to determine target
        if "create" in intent.action:
            return "new_item"
        elif "debug" in intent.action:
            return "existing_code"
        elif "search" in intent.action:
            return "information"
        
        # Default to first common target
        return common_targets[0] if common_targets else "general"
    
    def _determine_priority(self, intent: Intent) -> Priority:
        """Determine priority based on intent characteristics."""
        if intent.requires_confirmation:
            return Priority.HIGH
        elif intent.complexity == Complexity.EXPERT:
            return Priority.HIGH
        elif intent.complexity == Complexity.COMPLEX:
            return Priority.NORMAL
        else:
            return Priority.LOW
    
    def _generate_success_criteria(self, intent: Intent, action_plan: ActionPlan) -> List[str]:
        """Generate success criteria for the action plan."""
        criteria = []
        
        if action_plan.action_type == ActionType.CREATE:
            criteria.extend([
                "Target item is created successfully",
                "No errors during creation process",
                "Created item meets specified requirements"
            ])
        elif action_plan.action_type == ActionType.ANALYZE:
            criteria.extend([
                "Analysis completes without errors",
                "Findings are comprehensive and accurate",
                "Recommendations are actionable"
            ])
        elif action_plan.action_type == ActionType.EXECUTE:
            criteria.extend([
                "Command executes successfully",
                "Expected output is produced",
                "No system errors occur"
            ])
        
        return criteria
    
    def _identify_dependencies(self, intent: Intent, action_plan: ActionPlan) -> List[str]:
        """Identify dependencies for the action plan."""
        dependencies = []
        
        # Check for file dependencies
        for entity in intent.entities:
            if entity.type == "file_path":
                dependencies.append(f"File exists: {entity.value}")
        
        # Check for tool dependencies
        if action_plan.action_type == ActionType.EXECUTE:
            dependencies.append("Required tools are installed")
            dependencies.append("Proper permissions are available")
        
        return dependencies
    
    def _identify_action_risks(self, intent: Intent, action_plan: ActionPlan) -> List[str]:
        """Identify risks for the action plan."""
        risks = []
        
        if action_plan.action_type == ActionType.DELETE:
            risks.append("Data loss possible")
            risks.append("Irreversible operation")
        
        if action_plan.action_type == ActionType.EXECUTE:
            risks.append("System changes possible")
            risks.append("Unexpected side effects")
        
        return risks
    
    def _generate_supporting_plans(self, intent: Intent, primary_plan: ActionPlan) -> List[ActionPlan]:
        """Generate supporting action plans for complex intents."""
        supporting_plans = []
        
        # Add validation plan
        validation_plan = ActionPlan(
            action_type=ActionType.ANALYZE,
            target="validation",
            parameters={"validate_output": True},
            priority=Priority.NORMAL,
            estimated_duration=30.0,
            dependencies=[f"Primary action completed: {primary_plan.target}"]
        )
        supporting_plans.append(validation_plan)
        
        # Add testing plan for code generation
        if intent.category == IntentCategory.CODE_GENERATION:
            test_plan = ActionPlan(
                action_type=ActionType.CREATE,
                target="tests",
                parameters={"test_type": "unit", "coverage": "basic"},
                priority=Priority.NORMAL,
                estimated_duration=60.0,
                dependencies=[f"Code created: {primary_plan.target}"]
            )
            supporting_plans.append(test_plan)
        
        return supporting_plans
    
    def _create_workflow_steps(self, action_plans: List[ActionPlan], intent: Intent) -> List[WorkflowStep]:
        """Create workflow steps from action plans."""
        workflow_steps = []
        
        for i, plan in enumerate(action_plans):
            step = WorkflowStep(
                step_id=f"step_{i+1}",
                description=f"{plan.action_type.value.title()} {plan.target}",
                action_plan=plan,
                prerequisites=plan.dependencies,
                outputs=[f"Result of {plan.action_type.value}"],
                validation_rules=plan.success_criteria
            )
            workflow_steps.append(step)
        
        return workflow_steps
    
    def _assess_risks(self, intent: Intent, action_plans: List[ActionPlan]) -> str:
        """Assess overall risk level."""
        risk_scores = []
        
        # Check text patterns
        text = intent.metadata.get("original_text", "")
        for risk_level, patterns in self.risk_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    if risk_level == "high_risk":
                        risk_scores.append(3)
                    elif risk_level == "medium_risk":
                        risk_scores.append(2)
                    else:
                        risk_scores.append(1)
        
        # Check action plan risks
        for plan in action_plans:
            if plan.risks:
                risk_scores.append(2)
        
        # Determine overall risk
        if not risk_scores:
            return "low"
        
        avg_risk = sum(risk_scores) / len(risk_scores)
        if avg_risk >= 2.5:
            return "high"
        elif avg_risk >= 1.5:
            return "medium"
        else:
            return "low"
    
    def _calculate_confidence(self, intent: Intent, action_plans: List[ActionPlan]) -> float:
        """Calculate confidence score for the analysis."""
        base_confidence = intent.confidence
        
        # Boost confidence if we have specific action plans
        if action_plans:
            base_confidence += 0.1
        
        # Reduce confidence for complex intents
        if intent.complexity == Complexity.EXPERT:
            base_confidence -= 0.2
        elif intent.complexity == Complexity.COMPLEX:
            base_confidence -= 0.1
        
        return max(0.0, min(1.0, base_confidence))
    
    def _requires_confirmation(self, intent: Intent, action_plans: List[ActionPlan], risk_assessment: str) -> bool:
        """Determine if user confirmation is required."""
        # Always require confirmation for high-risk operations
        if risk_assessment == "high":
            return True
        
        # Require confirmation for complex operations
        if intent.complexity == Complexity.EXPERT:
            return True
        
        # Require confirmation for destructive actions
        for plan in action_plans:
            if plan.action_type == ActionType.DELETE:
                return True
            if "delete" in plan.target.lower() or "remove" in plan.target.lower():
                return True
        
        return intent.requires_confirmation
