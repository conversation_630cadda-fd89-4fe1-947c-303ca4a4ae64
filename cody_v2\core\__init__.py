#!/usr/bin/env python3

"""
CODY v2.0 Core Framework
Advanced SaaS AI Terminal Agent Core Components
"""

from .agent import (
    BaseAgent,
    CodyAgent,
    CodyConfig,
    AgentFactory,
    create_cody_agent,
    get_cody_agent
)

from .providers import (
    ProviderManager,
    DeepSeekProvider,
    GeminiProvider,
    ChatRequest,
    ChatResponse,
    ChatMessage
)

__all__ = [
    # Agent framework
    'BaseAgent',
    'CodyAgent', 
    'CodyConfig',
    'AgentFactory',
    'create_cody_agent',
    'get_cody_agent',
    
    # Provider system
    'ProviderManager',
    'DeepSeekProvider',
    'GeminiProvider',
    'ChatRequest',
    'ChatResponse',
    'ChatMessage'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Advanced SaaS AI Terminal Agent Core Framework"
