#!/usr/bin/env python3

"""
Advanced Natural Language Processing for CODY v2.0
Sophisticated NLP with multi-language support and context awareness
"""

import re
import logging
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from collections import defaultdict

logger = logging.getLogger('CODY.AdvancedNLP')

class Language(Enum):
    """Supported languages."""
    ENGLISH = "en"
    HINDI = "hi"
    MIXED = "mixed"
    AUTO_DETECT = "auto"

class IntentCategory(Enum):
    """High-level intent categories."""
    CODE_GENERATION = "code_generation"
    CODE_ANALYSIS = "code_analysis"
    CODE_DEBUGGING = "code_debugging"
    CODE_REFACTORING = "code_refactoring"
    FILE_OPERATIONS = "file_operations"
    TERMINAL_COMMANDS = "terminal_commands"
    WEB_SEARCH = "web_search"
    PROJECT_MANAGEMENT = "project_management"
    LEARNING_HELP = "learning_help"
    GENERAL_CHAT = "general_chat"
    SYSTEM_COMMANDS = "system_commands"

class Complexity(Enum):
    """Task complexity levels."""
    SIMPLE = 1
    MODERATE = 2
    COMPLEX = 3
    EXPERT = 4

@dataclass
class Entity:
    """Represents an extracted entity."""
    type: str
    value: str
    confidence: float
    start_pos: int
    end_pos: int
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Intent:
    """Represents detected intent."""
    category: IntentCategory
    action: str
    confidence: float
    entities: List[Entity]
    complexity: Complexity
    estimated_time: float  # in seconds
    requires_confirmation: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NLPResult:
    """Complete NLP analysis result."""
    original_text: str
    processed_text: str
    language: Language
    intent: Intent
    sentiment: str
    keywords: List[str]
    programming_languages: List[str]
    file_paths: List[str]
    urls: List[str]
    commands: List[str]
    processing_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class AdvancedNLPProcessor:
    """
    Advanced Natural Language Processing system for CODY v2.0.
    
    Features:
    - Multi-language support (English, Hindi, Mixed)
    - Context-aware intent recognition
    - Entity extraction and classification
    - Programming language detection
    - Complexity estimation
    - Sentiment analysis
    - Command and file path extraction
    """
    
    def __init__(self):
        """Initialize the advanced NLP processor."""
        self.language_patterns = self._initialize_language_patterns()
        self.intent_patterns = self._initialize_intent_patterns()
        self.entity_patterns = self._initialize_entity_patterns()
        self.programming_languages = self._initialize_programming_languages()
        self.context_history: List[NLPResult] = []
        self.max_context_history = 10
        
        logger.info("Advanced NLP processor initialized")
    
    def _initialize_language_patterns(self) -> Dict[Language, List[str]]:
        """Initialize language detection patterns."""
        return {
            Language.HINDI: [
                r'\b(kya|hai|kaise|karo|banao|create|file|folder|code|debug|fix|help|search)\b',
                r'\b(mujhe|chahiye|karna|hona|dekho|samjhao|batao|sikhao)\b',
                r'\b(isme|usse|waha|yaha|kaha|kaise|kyun|kya|kaun)\b',
                r'\b(aur|ya|lekin|par|phir|tab|jab|agar|to)\b'
            ],
            Language.ENGLISH: [
                r'\b(create|make|build|generate|write|code|develop|implement)\b',
                r'\b(debug|fix|solve|resolve|troubleshoot|analyze|check)\b',
                r'\b(search|find|look|browse|fetch|get|retrieve)\b',
                r'\b(help|assist|guide|explain|show|teach|learn)\b'
            ]
        }
    
    def _initialize_intent_patterns(self) -> Dict[IntentCategory, List[Dict[str, Any]]]:
        """Initialize intent recognition patterns."""
        return {
            IntentCategory.CODE_GENERATION: [
                {
                    "patterns": [
                        r'\b(create|make|build|generate|write|develop|implement|code)\b.*\b(app|application|project|program|script|function|class|component)\b',
                        r'\b(banao|create|bnao)\b.*\b(app|website|project|code|program)\b',
                        r'\b(new|fresh|from scratch)\b.*\b(project|app|code)\b'
                    ],
                    "actions": ["create_project", "generate_code", "scaffold_app"],
                    "complexity": Complexity.MODERATE,
                    "estimated_time": 300.0
                }
            ],
            IntentCategory.CODE_DEBUGGING: [
                {
                    "patterns": [
                        r'\b(debug|fix|solve|resolve|troubleshoot|error|bug|issue|problem)\b',
                        r'\b(not working|broken|failing|crash|exception)\b',
                        r'\b(fix karo|debug karo|error hai|problem hai)\b'
                    ],
                    "actions": ["debug_code", "fix_error", "analyze_issue"],
                    "complexity": Complexity.COMPLEX,
                    "estimated_time": 180.0
                }
            ],
            IntentCategory.FILE_OPERATIONS: [
                {
                    "patterns": [
                        r'\b(create|make|delete|remove|move|copy|rename)\b.*\b(file|folder|directory)\b',
                        r'\b(read|write|edit|modify|update)\b.*\b(file|document)\b',
                        r'\b(file|folder|directory)\b.*\b(banao|create|delete|move)\b'
                    ],
                    "actions": ["file_operation", "directory_management"],
                    "complexity": Complexity.SIMPLE,
                    "estimated_time": 30.0
                }
            ],
            IntentCategory.TERMINAL_COMMANDS: [
                {
                    "patterns": [
                        r'\b(run|execute|command|terminal|shell|bash|cmd)\b',
                        r'\b(install|npm|pip|yarn|cargo|go get)\b',
                        r'\b(git|docker|kubectl|ssh)\b'
                    ],
                    "actions": ["execute_command", "run_script"],
                    "complexity": Complexity.MODERATE,
                    "estimated_time": 60.0
                }
            ],
            IntentCategory.WEB_SEARCH: [
                {
                    "patterns": [
                        r'\b(search|find|look|browse|google|documentation|docs)\b',
                        r'\b(best practices|tutorial|guide|example|how to)\b',
                        r'\b(search karo|find karo|dhundo|dekho)\b'
                    ],
                    "actions": ["web_search", "documentation_lookup"],
                    "complexity": Complexity.SIMPLE,
                    "estimated_time": 45.0
                }
            ],
            IntentCategory.SYSTEM_COMMANDS: [
                {
                    "patterns": [
                        r'^/\w+',  # Commands starting with /
                        r'\b(switch|change|configure|settings|config)\b',
                        r'\b(help|status|info|version)\b'
                    ],
                    "actions": ["system_command", "configuration"],
                    "complexity": Complexity.SIMPLE,
                    "estimated_time": 5.0
                }
            ]
        }
    
    def _initialize_entity_patterns(self) -> Dict[str, str]:
        """Initialize entity extraction patterns."""
        return {
            "file_path": r'(?:[a-zA-Z]:\\|/)?(?:[^/\\:*?"<>|\r\n]+[/\\])*[^/\\:*?"<>|\r\n]*\.[a-zA-Z0-9]+',
            "url": r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?',
            "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "version": r'\b\d+\.\d+(?:\.\d+)?(?:-[a-zA-Z0-9]+)?\b',
            "port": r'\b(?:port\s+)?(\d{1,5})\b',
            "ip_address": r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
            "function_name": r'\b(?:function|def|func)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            "class_name": r'\b(?:class|interface|struct)\s+([A-Z][a-zA-Z0-9_]*)',
            "variable_name": r'\b(?:var|let|const|int|string|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            "package_name": r'\b(?:import|from|require|use)\s+([a-zA-Z0-9._-]+)',
            "command": r'\b(?:npm|pip|yarn|cargo|go|docker|git|kubectl)\s+([a-zA-Z0-9\-_]+)',
            "framework": r'\b(react|vue|angular|django|flask|express|spring|laravel|rails)\b',
            "database": r'\b(mysql|postgresql|mongodb|redis|sqlite|oracle|cassandra)\b'
        }
    
    def _initialize_programming_languages(self) -> Dict[str, List[str]]:
        """Initialize programming language detection patterns."""
        return {
            "python": ["python", "py", "django", "flask", "pandas", "numpy", "pip", "conda"],
            "javascript": ["javascript", "js", "node", "npm", "react", "vue", "angular", "express"],
            "typescript": ["typescript", "ts", "tsx", "angular", "nest"],
            "java": ["java", "spring", "maven", "gradle", "junit"],
            "go": ["go", "golang", "gin", "echo", "gorilla"],
            "rust": ["rust", "cargo", "tokio", "serde", "actix"],
            "cpp": ["c++", "cpp", "cmake", "boost", "qt"],
            "c": ["c", "gcc", "clang", "make"],
            "csharp": ["c#", "csharp", "dotnet", ".net", "asp.net"],
            "php": ["php", "laravel", "symfony", "composer"],
            "ruby": ["ruby", "rails", "gem", "bundler"],
            "swift": ["swift", "ios", "xcode", "cocoapods"],
            "kotlin": ["kotlin", "android", "gradle"],
            "dart": ["dart", "flutter", "pub"],
            "scala": ["scala", "sbt", "akka"],
            "r": ["r", "rstudio", "cran", "ggplot"],
            "matlab": ["matlab", "simulink"],
            "shell": ["bash", "zsh", "fish", "powershell", "cmd"],
            "sql": ["sql", "mysql", "postgresql", "sqlite", "oracle"],
            "html": ["html", "css", "sass", "scss", "less"],
            "docker": ["docker", "dockerfile", "compose", "kubernetes"],
            "terraform": ["terraform", "hcl", "aws", "azure", "gcp"]
        }
    
    async def process(self, text: str, context: Optional[Dict[str, Any]] = None) -> NLPResult:
        """
        Process text with advanced NLP analysis.
        
        Args:
            text: Input text to process
            context: Optional context information
            
        Returns:
            Complete NLP analysis result
        """
        start_time = time.time()
        
        try:
            # Preprocess text
            processed_text = self._preprocess_text(text)
            
            # Detect language
            language = self._detect_language(processed_text)
            
            # Extract entities
            entities = self._extract_entities(processed_text)
            
            # Detect intent
            intent = self._detect_intent(processed_text, entities, context)
            
            # Analyze sentiment
            sentiment = self._analyze_sentiment(processed_text)
            
            # Extract keywords
            keywords = self._extract_keywords(processed_text)
            
            # Detect programming languages
            prog_languages = self._detect_programming_languages(processed_text)
            
            # Extract file paths, URLs, commands
            file_paths = self._extract_file_paths(processed_text)
            urls = self._extract_urls(processed_text)
            commands = self._extract_commands(processed_text)
            
            processing_time = time.time() - start_time
            
            result = NLPResult(
                original_text=text,
                processed_text=processed_text,
                language=language,
                intent=intent,
                sentiment=sentiment,
                keywords=keywords,
                programming_languages=prog_languages,
                file_paths=file_paths,
                urls=urls,
                commands=commands,
                processing_time=processing_time,
                metadata={
                    "context_used": context is not None,
                    "entity_count": len(entities),
                    "confidence_score": intent.confidence
                }
            )
            
            # Add to context history
            self._update_context_history(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in NLP processing: {e}")
            # Return basic result on error
            return NLPResult(
                original_text=text,
                processed_text=text,
                language=Language.AUTO_DETECT,
                intent=Intent(
                    category=IntentCategory.GENERAL_CHAT,
                    action="general_response",
                    confidence=0.5,
                    entities=[],
                    complexity=Complexity.SIMPLE,
                    estimated_time=30.0
                ),
                sentiment="neutral",
                keywords=[],
                programming_languages=[],
                file_paths=[],
                urls=[],
                commands=[],
                processing_time=time.time() - start_time
            )
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for analysis."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Normalize common variations
        text = re.sub(r'\bapp\b', 'application', text, flags=re.IGNORECASE)
        text = re.sub(r'\bjs\b', 'javascript', text, flags=re.IGNORECASE)
        text = re.sub(r'\bpy\b', 'python', text, flags=re.IGNORECASE)
        
        return text
    
    def _detect_language(self, text: str) -> Language:
        """Detect the language of the text."""
        hindi_score = 0
        english_score = 0
        
        text_lower = text.lower()
        
        # Check Hindi patterns
        for pattern in self.language_patterns[Language.HINDI]:
            hindi_score += len(re.findall(pattern, text_lower))
        
        # Check English patterns
        for pattern in self.language_patterns[Language.ENGLISH]:
            english_score += len(re.findall(pattern, text_lower))
        
        if hindi_score > 0 and english_score > 0:
            return Language.MIXED
        elif hindi_score > english_score:
            return Language.HINDI
        elif english_score > 0:
            return Language.ENGLISH
        else:
            return Language.AUTO_DETECT
    
    def _extract_entities(self, text: str) -> List[Entity]:
        """Extract entities from text."""
        entities = []
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    type=entity_type,
                    value=match.group(0),
                    confidence=0.8,  # Base confidence
                    start_pos=match.start(),
                    end_pos=match.end()
                )
                entities.append(entity)
        
        return entities
    
    def _detect_intent(self, text: str, entities: List[Entity], context: Optional[Dict[str, Any]]) -> Intent:
        """Detect intent from text and entities."""
        text_lower = text.lower()
        best_intent = None
        best_confidence = 0.0
        
        for category, patterns_list in self.intent_patterns.items():
            for pattern_info in patterns_list:
                confidence = 0.0
                matched_patterns = 0
                
                for pattern in pattern_info["patterns"]:
                    if re.search(pattern, text_lower):
                        matched_patterns += 1
                        confidence += 0.3
                
                # Boost confidence based on entities
                for entity in entities:
                    if entity.type in ["file_path", "function_name", "class_name"]:
                        confidence += 0.1
                
                # Context boost
                if context and self._is_context_relevant(category, context):
                    confidence += 0.2
                
                # Normalize confidence
                confidence = min(1.0, confidence)
                
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_intent = Intent(
                        category=category,
                        action=pattern_info["actions"][0],
                        confidence=confidence,
                        entities=entities,
                        complexity=pattern_info["complexity"],
                        estimated_time=pattern_info["estimated_time"],
                        requires_confirmation=pattern_info["complexity"] in [Complexity.COMPLEX, Complexity.EXPERT]
                    )
        
        # Default intent if no match
        if not best_intent:
            best_intent = Intent(
                category=IntentCategory.GENERAL_CHAT,
                action="general_response",
                confidence=0.5,
                entities=entities,
                complexity=Complexity.SIMPLE,
                estimated_time=30.0
            )
        
        return best_intent
    
    def _is_context_relevant(self, category: IntentCategory, context: Dict[str, Any]) -> bool:
        """Check if context is relevant to the intent category."""
        # Simple context relevance check
        if "last_action" in context:
            last_action = context["last_action"]
            if category == IntentCategory.CODE_DEBUGGING and "error" in last_action:
                return True
            if category == IntentCategory.FILE_OPERATIONS and "file" in last_action:
                return True
        
        return False
    
    def _analyze_sentiment(self, text: str) -> str:
        """Analyze sentiment of the text."""
        positive_words = ["good", "great", "excellent", "awesome", "perfect", "love", "like"]
        negative_words = ["bad", "terrible", "awful", "hate", "problem", "error", "issue", "broken"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text."""
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Filter out common words
        stop_words = {"the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "its", "may", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "man", "men", "put", "say", "she", "too", "use"}
        
        keywords = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Return unique keywords
        return list(set(keywords))
    
    def _detect_programming_languages(self, text: str) -> List[str]:
        """Detect programming languages mentioned in text."""
        detected_languages = []
        text_lower = text.lower()
        
        for language, keywords in self.programming_languages.items():
            for keyword in keywords:
                if keyword in text_lower:
                    detected_languages.append(language)
                    break
        
        return list(set(detected_languages))
    
    def _extract_file_paths(self, text: str) -> List[str]:
        """Extract file paths from text."""
        pattern = self.entity_patterns["file_path"]
        matches = re.findall(pattern, text)
        return list(set(matches))
    
    def _extract_urls(self, text: str) -> List[str]:
        """Extract URLs from text."""
        pattern = self.entity_patterns["url"]
        matches = re.findall(pattern, text)
        return list(set(matches))
    
    def _extract_commands(self, text: str) -> List[str]:
        """Extract commands from text."""
        pattern = self.entity_patterns["command"]
        matches = re.findall(pattern, text)
        return list(set(matches))
    
    def _update_context_history(self, result: NLPResult) -> None:
        """Update context history with new result."""
        self.context_history.append(result)
        
        # Keep only recent history
        if len(self.context_history) > self.max_context_history:
            self.context_history = self.context_history[-self.max_context_history:]
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get summary of recent context."""
        if not self.context_history:
            return {}
        
        recent_intents = [r.intent.category.value for r in self.context_history[-5:]]
        recent_languages = []
        for r in self.context_history[-5:]:
            recent_languages.extend(r.programming_languages)
        
        return {
            "recent_intents": recent_intents,
            "recent_programming_languages": list(set(recent_languages)),
            "last_complexity": self.context_history[-1].intent.complexity.value if self.context_history else 1,
            "conversation_length": len(self.context_history)
        }
