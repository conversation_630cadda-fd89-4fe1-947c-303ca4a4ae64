#!/usr/bin/env python3

"""
Agent Factory for CODY v2.0
Manages agent creation, configuration, and lifecycle
"""

import logging
import threading
from typing import Dict, Optional, Type, Any
from dataclasses import dataclass
from enum import Enum

from .base_agent import BaseAgent
from .cody_agent import CodyAgent, CodyConfig

logger = logging.getLogger('CODY.AgentFactory')

class AgentType(Enum):
    """Available agent types."""
    CODY = "cody"
    SPECIALIZED = "specialized"
    CUSTOM = "custom"

@dataclass
class AgentInstance:
    """Represents an agent instance."""
    id: str
    agent: BaseAgent
    agent_type: AgentType
    config: Dict[str, Any]
    created_at: float
    last_used: float

class AgentFactory:
    """
    Factory for creating and managing agent instances.
    Supports multiple agent types and configurations.
    """
    
    def __init__(self):
        """Initialize the agent factory."""
        self.agents: Dict[str, AgentInstance] = {}
        self.agent_types: Dict[AgentType, Type[BaseAgent]] = {
            AgentType.CODY: CodyAgent
        }
        self.default_configs: Dict[AgentType, Any] = {
            AgentType.CODY: CodyConfig()
        }
        self.lock = threading.RLock()
        
        logger.info("Agent factory initialized")
    
    def create_agent(self, 
                    agent_type: AgentType = AgentType.CODY,
                    agent_id: Optional[str] = None,
                    config: Optional[Any] = None) -> str:
        """
        Create a new agent instance.
        
        Args:
            agent_type: Type of agent to create
            agent_id: Optional custom agent ID
            config: Optional agent configuration
            
        Returns:
            Agent instance ID
        """
        import time
        import uuid
        
        if agent_id is None:
            agent_id = f"{agent_type.value}_{str(uuid.uuid4())[:8]}"
        
        with self.lock:
            if agent_id in self.agents:
                raise ValueError(f"Agent with ID {agent_id} already exists")
            
            # Get agent class
            agent_class = self.agent_types.get(agent_type)
            if not agent_class:
                raise ValueError(f"Unsupported agent type: {agent_type}")
            
            # Use provided config or default
            if config is None:
                config = self.default_configs.get(agent_type)
            
            # Create agent instance
            try:
                if agent_type == AgentType.CODY:
                    agent = agent_class(config)
                else:
                    agent = agent_class()
                
                # Create agent instance record
                instance = AgentInstance(
                    id=agent_id,
                    agent=agent,
                    agent_type=agent_type,
                    config=config.__dict__ if hasattr(config, '__dict__') else config or {},
                    created_at=time.time(),
                    last_used=time.time()
                )
                
                self.agents[agent_id] = instance
                
                logger.info(f"Created {agent_type.value} agent with ID: {agent_id}")
                return agent_id
                
            except Exception as e:
                logger.error(f"Failed to create {agent_type.value} agent: {e}")
                raise
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """
        Get an agent instance by ID.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            Agent instance or None if not found
        """
        with self.lock:
            instance = self.agents.get(agent_id)
            if instance:
                import time
                instance.last_used = time.time()
                return instance.agent
            return None
    
    def list_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        List all agent instances.
        
        Returns:
            Dictionary of agent information
        """
        with self.lock:
            return {
                agent_id: {
                    "type": instance.agent_type.value,
                    "state": instance.agent.state.value,
                    "created_at": instance.created_at,
                    "last_used": instance.last_used,
                    "capabilities": len(instance.agent.capabilities),
                    "active_tasks": len(instance.agent.active_tasks)
                }
                for agent_id, instance in self.agents.items()
            }
    
    def remove_agent(self, agent_id: str) -> bool:
        """
        Remove an agent instance.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            True if agent was removed, False if not found
        """
        with self.lock:
            instance = self.agents.get(agent_id)
            if instance:
                # Shutdown the agent gracefully
                try:
                    instance.agent.shutdown()
                except Exception as e:
                    logger.warning(f"Error shutting down agent {agent_id}: {e}")
                
                del self.agents[agent_id]
                logger.info(f"Removed agent: {agent_id}")
                return True
            return False
    
    def shutdown_all(self) -> None:
        """Shutdown all agent instances."""
        with self.lock:
            for agent_id, instance in list(self.agents.items()):
                try:
                    instance.agent.shutdown()
                    logger.info(f"Shutdown agent: {agent_id}")
                except Exception as e:
                    logger.error(f"Error shutting down agent {agent_id}: {e}")
            
            self.agents.clear()
            logger.info("All agents shutdown")
    
    def register_agent_type(self, 
                           agent_type: AgentType, 
                           agent_class: Type[BaseAgent],
                           default_config: Optional[Any] = None) -> None:
        """
        Register a new agent type.
        
        Args:
            agent_type: Agent type enum
            agent_class: Agent class
            default_config: Default configuration for this agent type
        """
        self.agent_types[agent_type] = agent_class
        if default_config:
            self.default_configs[agent_type] = default_config
        
        logger.info(f"Registered agent type: {agent_type.value}")
    
    def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed status of an agent.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            Agent status dictionary or None if not found
        """
        with self.lock:
            instance = self.agents.get(agent_id)
            if instance:
                status = instance.agent.get_agent_status()
                status.update({
                    "instance_id": agent_id,
                    "agent_type": instance.agent_type.value,
                    "created_at": instance.created_at,
                    "last_used": instance.last_used,
                    "config": instance.config
                })
                return status
            return None
    
    def update_agent_config(self, agent_id: str, config_updates: Dict[str, Any]) -> bool:
        """
        Update agent configuration.
        
        Args:
            agent_id: Agent instance ID
            config_updates: Configuration updates to apply
            
        Returns:
            True if update was successful, False otherwise
        """
        with self.lock:
            instance = self.agents.get(agent_id)
            if instance:
                try:
                    # Update instance config
                    instance.config.update(config_updates)
                    
                    # If it's a CODY agent, update the agent's config
                    if isinstance(instance.agent, CodyAgent):
                        for key, value in config_updates.items():
                            if hasattr(instance.agent.config, key):
                                setattr(instance.agent.config, key, value)
                    
                    logger.info(f"Updated config for agent {agent_id}")
                    return True
                    
                except Exception as e:
                    logger.error(f"Failed to update config for agent {agent_id}: {e}")
                    return False
            return False

# Global agent factory instance
agent_factory = AgentFactory()

def create_cody_agent(config: Optional[CodyConfig] = None) -> str:
    """
    Convenience function to create a CODY agent.
    
    Args:
        config: Optional CODY configuration
        
    Returns:
        Agent instance ID
    """
    return agent_factory.create_agent(AgentType.CODY, config=config)

def get_cody_agent(agent_id: str) -> Optional[CodyAgent]:
    """
    Convenience function to get a CODY agent.
    
    Args:
        agent_id: Agent instance ID
        
    Returns:
        CODY agent instance or None
    """
    agent = agent_factory.get_agent(agent_id)
    if isinstance(agent, CodyAgent):
        return agent
    return None
