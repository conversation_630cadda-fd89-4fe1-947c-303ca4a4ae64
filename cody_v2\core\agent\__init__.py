#!/usr/bin/env python3

"""
CODY v2.0 Agent Framework
Core agent implementation with multi-model support and autonomous capabilities
"""

from .base_agent import (
    BaseAgent,
    AgentState,
    TaskPriority,
    TaskStatus,
    AgentCapability,
    TaskRequest,
    TaskResult,
    AgentMetrics
)

from .cody_agent import (
    CodyAgent,
    CodyConfig
)

from .agent_factory import (
    AgentFactory,
    AgentType,
    AgentInstance,
    agent_factory,
    create_cody_agent,
    get_cody_agent
)

__all__ = [
    # Base agent classes
    'BaseAgent',
    'AgentState',
    'TaskPriority', 
    'TaskStatus',
    'AgentCapability',
    'TaskRequest',
    'TaskResult',
    'AgentMetrics',
    
    # CODY agent
    'CodyAgent',
    'CodyConfig',
    
    # Agent factory
    'AgentFactory',
    'AgentType',
    'AgentInstance',
    'agent_factory',
    'create_cody_agent',
    'get_cody_agent'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Advanced SaaS AI Terminal Agent Framework"
