#!/usr/bin/env python3

"""
AI Provider System for CODY v2.0
Multi-model AI provider support with dynamic switching and load balancing
"""

from .base_provider import (
    BaseAIProvider,
    ModelType,
    ProviderStatus,
    ModelInfo,
    ProviderConfig,
    ChatMessage,
    ChatRequest,
    ChatResponse,
    ProviderMetrics
)

from .deepseek_provider import DeepSeekProvider
from .gemini_provider import GeminiProvider

from .provider_manager import (
    ProviderManager,
    LoadBalancingStrategy,
    ProviderHealth
)

__all__ = [
    # Base provider classes
    'BaseAIProvider',
    'ModelType',
    'ProviderStatus',
    'ModelInfo',
    'ProviderConfig',
    'ChatMessage',
    'ChatRequest',
    'ChatResponse',
    'ProviderMetrics',
    
    # Specific providers
    'DeepSeekProvider',
    'GeminiProvider',
    
    # Provider management
    'ProviderManager',
    'LoadBalancingStrategy',
    'ProviderHealth'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Multi-model AI provider system with dynamic switching"
