#!/usr/bin/env python3

"""
Smart File Operations for CODY v2.0
Intelligent file handling with context awareness and safety features
"""

import os
import shutil
import asyncio
import logging
import hashlib
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import time

logger = logging.getLogger('CODY.SmartFileOps')

class FileOperation(Enum):
    """Types of file operations."""
    CREATE = "create"
    READ = "read"
    WRITE = "write"
    UPDATE = "update"
    DELETE = "delete"
    COPY = "copy"
    MOVE = "move"
    RENAME = "rename"
    BACKUP = "backup"
    RESTORE = "restore"

class FileType(Enum):
    """File type categories."""
    TEXT = "text"
    CODE = "code"
    CONFIG = "config"
    DATA = "data"
    BINARY = "binary"
    IMAGE = "image"
    DOCUMENT = "document"
    ARCHIVE = "archive"
    UNKNOWN = "unknown"

@dataclass
class FileInfo:
    """Comprehensive file information."""
    path: str
    name: str
    extension: str
    size: int
    file_type: FileType
    mime_type: str
    created_at: float
    modified_at: float
    permissions: str
    is_directory: bool
    is_symlink: bool
    checksum: Optional[str] = None
    encoding: Optional[str] = None
    line_count: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class OperationResult:
    """Result of a file operation."""
    operation: FileOperation
    success: bool
    source_path: Optional[str] = None
    target_path: Optional[str] = None
    message: str = ""
    error: Optional[str] = None
    backup_path: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class SmartFileOperations:
    """
    Smart file operations with intelligent handling and safety features.
    
    Features:
    - Automatic backup before destructive operations
    - File type detection and appropriate handling
    - Encoding detection and conversion
    - Safe operations with validation
    - Batch operations with progress tracking
    - Undo/redo capabilities
    """
    
    def __init__(self, 
                 backup_enabled: bool = True,
                 backup_dir: str = ".cody_backups",
                 max_backups: int = 10):
        """
        Initialize smart file operations.
        
        Args:
            backup_enabled: Enable automatic backups
            backup_dir: Directory for backups
            max_backups: Maximum number of backups to keep
        """
        self.backup_enabled = backup_enabled
        self.backup_dir = backup_dir
        self.max_backups = max_backups
        
        # File type mappings
        self.file_type_mappings = self._initialize_file_type_mappings()
        self.code_extensions = self._initialize_code_extensions()
        
        # Operation history for undo/redo
        self.operation_history: List[OperationResult] = []
        self.max_history = 50
        
        # Ensure backup directory exists
        if self.backup_enabled:
            os.makedirs(self.backup_dir, exist_ok=True)
        
        logger.info("Smart file operations initialized")
    
    def _initialize_file_type_mappings(self) -> Dict[str, FileType]:
        """Initialize file extension to type mappings."""
        return {
            # Code files
            '.py': FileType.CODE, '.js': FileType.CODE, '.ts': FileType.CODE,
            '.java': FileType.CODE, '.cpp': FileType.CODE, '.c': FileType.CODE,
            '.go': FileType.CODE, '.rs': FileType.CODE, '.php': FileType.CODE,
            '.rb': FileType.CODE, '.swift': FileType.CODE, '.kt': FileType.CODE,
            '.dart': FileType.CODE, '.scala': FileType.CODE, '.r': FileType.CODE,
            '.m': FileType.CODE, '.h': FileType.CODE, '.hpp': FileType.CODE,
            
            # Web files
            '.html': FileType.CODE, '.css': FileType.CODE, '.scss': FileType.CODE,
            '.sass': FileType.CODE, '.less': FileType.CODE, '.jsx': FileType.CODE,
            '.tsx': FileType.CODE, '.vue': FileType.CODE, '.svelte': FileType.CODE,
            
            # Config files
            '.json': FileType.CONFIG, '.yaml': FileType.CONFIG, '.yml': FileType.CONFIG,
            '.toml': FileType.CONFIG, '.ini': FileType.CONFIG, '.cfg': FileType.CONFIG,
            '.conf': FileType.CONFIG, '.xml': FileType.CONFIG, '.env': FileType.CONFIG,
            
            # Text files
            '.txt': FileType.TEXT, '.md': FileType.TEXT, '.rst': FileType.TEXT,
            '.log': FileType.TEXT, '.csv': FileType.DATA, '.tsv': FileType.DATA,
            
            # Documents
            '.pdf': FileType.DOCUMENT, '.doc': FileType.DOCUMENT, '.docx': FileType.DOCUMENT,
            '.xls': FileType.DOCUMENT, '.xlsx': FileType.DOCUMENT, '.ppt': FileType.DOCUMENT,
            '.pptx': FileType.DOCUMENT,
            
            # Images
            '.jpg': FileType.IMAGE, '.jpeg': FileType.IMAGE, '.png': FileType.IMAGE,
            '.gif': FileType.IMAGE, '.bmp': FileType.IMAGE, '.svg': FileType.IMAGE,
            '.ico': FileType.IMAGE, '.webp': FileType.IMAGE,
            
            # Archives
            '.zip': FileType.ARCHIVE, '.tar': FileType.ARCHIVE, '.gz': FileType.ARCHIVE,
            '.rar': FileType.ARCHIVE, '.7z': FileType.ARCHIVE, '.bz2': FileType.ARCHIVE,
            
            # Data files
            '.db': FileType.DATA, '.sqlite': FileType.DATA, '.sql': FileType.DATA,
            '.parquet': FileType.DATA, '.arrow': FileType.DATA,
            
            # Binary files
            '.exe': FileType.BINARY, '.dll': FileType.BINARY, '.so': FileType.BINARY,
            '.dylib': FileType.BINARY, '.bin': FileType.BINARY
        }
    
    def _initialize_code_extensions(self) -> Dict[str, str]:
        """Initialize code file extensions to language mappings."""
        return {
            '.py': 'python', '.js': 'javascript', '.ts': 'typescript',
            '.java': 'java', '.cpp': 'cpp', '.c': 'c', '.go': 'go',
            '.rs': 'rust', '.php': 'php', '.rb': 'ruby', '.swift': 'swift',
            '.kt': 'kotlin', '.dart': 'dart', '.scala': 'scala', '.r': 'r',
            '.html': 'html', '.css': 'css', '.scss': 'scss', '.sass': 'sass',
            '.jsx': 'jsx', '.tsx': 'tsx', '.vue': 'vue', '.svelte': 'svelte'
        }
    
    async def get_file_info(self, file_path: str) -> Optional[FileInfo]:
        """
        Get comprehensive information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            FileInfo object or None if file doesn't exist
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return None
            
            stat = path.stat()
            
            # Basic info
            file_info = FileInfo(
                path=str(path.absolute()),
                name=path.name,
                extension=path.suffix.lower(),
                size=stat.st_size,
                file_type=self._detect_file_type(path),
                mime_type=mimetypes.guess_type(str(path))[0] or "unknown",
                created_at=stat.st_ctime,
                modified_at=stat.st_mtime,
                permissions=oct(stat.st_mode)[-3:],
                is_directory=path.is_dir(),
                is_symlink=path.is_symlink()
            )
            
            # Additional info for text files
            if file_info.file_type in [FileType.TEXT, FileType.CODE, FileType.CONFIG]:
                try:
                    encoding = await self._detect_encoding(path)
                    file_info.encoding = encoding
                    
                    if file_info.size < 10 * 1024 * 1024:  # Only for files < 10MB
                        with open(path, 'r', encoding=encoding) as f:
                            content = f.read()
                            file_info.line_count = len(content.splitlines())
                            file_info.checksum = hashlib.md5(content.encode()).hexdigest()
                except Exception as e:
                    logger.warning(f"Could not read text file {path}: {e}")
            
            # Checksum for binary files
            elif file_info.size < 100 * 1024 * 1024:  # Only for files < 100MB
                try:
                    file_info.checksum = await self._calculate_checksum(path)
                except Exception as e:
                    logger.warning(f"Could not calculate checksum for {path}: {e}")
            
            return file_info
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return None
    
    def _detect_file_type(self, path: Path) -> FileType:
        """Detect file type based on extension and content."""
        extension = path.suffix.lower()
        
        if extension in self.file_type_mappings:
            return self.file_type_mappings[extension]
        
        # Special cases
        if path.name.lower() in ['dockerfile', 'makefile', 'rakefile']:
            return FileType.CONFIG
        
        if path.name.lower().startswith('.'):
            return FileType.CONFIG
        
        return FileType.UNKNOWN
    
    async def _detect_encoding(self, path: Path) -> str:
        """Detect file encoding."""
        try:
            # Try common encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(path, 'r', encoding=encoding) as f:
                        f.read(1024)  # Read first 1KB
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            return 'utf-8'  # Default fallback
            
        except Exception:
            return 'utf-8'
    
    async def _calculate_checksum(self, path: Path) -> str:
        """Calculate MD5 checksum of a file."""
        hash_md5 = hashlib.md5()
        
        with open(path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    async def create_file(self, 
                         file_path: str, 
                         content: str = "", 
                         encoding: str = "utf-8",
                         overwrite: bool = False) -> OperationResult:
        """
        Create a new file with content.
        
        Args:
            file_path: Path for the new file
            content: File content
            encoding: File encoding
            overwrite: Whether to overwrite existing file
            
        Returns:
            Operation result
        """
        try:
            path = Path(file_path)
            
            # Check if file exists
            if path.exists() and not overwrite:
                return OperationResult(
                    operation=FileOperation.CREATE,
                    success=False,
                    target_path=str(path),
                    error="File already exists and overwrite is False"
                )
            
            # Create backup if file exists and backup is enabled
            backup_path = None
            if path.exists() and self.backup_enabled:
                backup_path = await self._create_backup(path)
            
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            
            result = OperationResult(
                operation=FileOperation.CREATE,
                success=True,
                target_path=str(path),
                message=f"File created successfully: {path.name}",
                backup_path=backup_path,
                metadata={
                    "size": len(content.encode(encoding)),
                    "encoding": encoding,
                    "line_count": len(content.splitlines())
                }
            )
            
            self._add_to_history(result)
            return result
            
        except Exception as e:
            logger.error(f"Error creating file {file_path}: {e}")
            return OperationResult(
                operation=FileOperation.CREATE,
                success=False,
                target_path=file_path,
                error=str(e)
            )
    
    async def read_file(self, 
                       file_path: str, 
                       encoding: Optional[str] = None,
                       max_size: int = 10 * 1024 * 1024) -> Tuple[bool, str, Optional[str]]:
        """
        Read file content safely.
        
        Args:
            file_path: Path to the file
            encoding: File encoding (auto-detect if None)
            max_size: Maximum file size to read
            
        Returns:
            Tuple of (success, content, error)
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return False, "", "File does not exist"
            
            if path.stat().st_size > max_size:
                return False, "", f"File too large (>{max_size} bytes)"
            
            if encoding is None:
                encoding = await self._detect_encoding(path)
            
            with open(path, 'r', encoding=encoding) as f:
                content = f.read()
            
            return True, content, None
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return False, "", str(e)
    
    async def write_file(self, 
                        file_path: str, 
                        content: str,
                        encoding: str = "utf-8",
                        create_backup: bool = True) -> OperationResult:
        """
        Write content to a file with backup.
        
        Args:
            file_path: Path to the file
            content: Content to write
            encoding: File encoding
            create_backup: Whether to create backup
            
        Returns:
            Operation result
        """
        try:
            path = Path(file_path)
            
            # Create backup if file exists
            backup_path = None
            if path.exists() and create_backup and self.backup_enabled:
                backup_path = await self._create_backup(path)
            
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            
            result = OperationResult(
                operation=FileOperation.WRITE,
                success=True,
                target_path=str(path),
                message=f"File written successfully: {path.name}",
                backup_path=backup_path,
                metadata={
                    "size": len(content.encode(encoding)),
                    "encoding": encoding,
                    "line_count": len(content.splitlines())
                }
            )
            
            self._add_to_history(result)
            return result
            
        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            return OperationResult(
                operation=FileOperation.WRITE,
                success=False,
                target_path=file_path,
                error=str(e)
            )
    
    async def delete_file(self, file_path: str, create_backup: bool = True) -> OperationResult:
        """
        Delete a file with optional backup.
        
        Args:
            file_path: Path to the file
            create_backup: Whether to create backup before deletion
            
        Returns:
            Operation result
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return OperationResult(
                    operation=FileOperation.DELETE,
                    success=False,
                    source_path=str(path),
                    error="File does not exist"
                )
            
            # Create backup if enabled
            backup_path = None
            if create_backup and self.backup_enabled:
                backup_path = await self._create_backup(path)
            
            # Delete file
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()
            
            result = OperationResult(
                operation=FileOperation.DELETE,
                success=True,
                source_path=str(path),
                message=f"File deleted successfully: {path.name}",
                backup_path=backup_path
            )
            
            self._add_to_history(result)
            return result
            
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return OperationResult(
                operation=FileOperation.DELETE,
                success=False,
                source_path=file_path,
                error=str(e)
            )
    
    async def _create_backup(self, path: Path) -> str:
        """Create a backup of a file."""
        timestamp = int(time.time())
        backup_name = f"{path.name}.backup.{timestamp}"
        backup_path = Path(self.backup_dir) / backup_name
        
        # Ensure backup directory exists
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy file
        if path.is_dir():
            shutil.copytree(path, backup_path)
        else:
            shutil.copy2(path, backup_path)
        
        # Clean old backups
        await self._cleanup_old_backups()
        
        return str(backup_path)
    
    async def _cleanup_old_backups(self):
        """Clean up old backup files."""
        try:
            backup_dir = Path(self.backup_dir)
            if not backup_dir.exists():
                return
            
            # Get all backup files sorted by creation time
            backups = []
            for backup_file in backup_dir.iterdir():
                if backup_file.name.endswith('.backup'):
                    backups.append((backup_file.stat().st_ctime, backup_file))
            
            # Sort by creation time (newest first)
            backups.sort(reverse=True)
            
            # Remove old backups
            if len(backups) > self.max_backups:
                for _, backup_file in backups[self.max_backups:]:
                    try:
                        if backup_file.is_dir():
                            shutil.rmtree(backup_file)
                        else:
                            backup_file.unlink()
                    except Exception as e:
                        logger.warning(f"Could not remove old backup {backup_file}: {e}")
        
        except Exception as e:
            logger.warning(f"Error cleaning up old backups: {e}")
    
    def _add_to_history(self, result: OperationResult):
        """Add operation result to history."""
        self.operation_history.append(result)
        
        # Keep only recent history
        if len(self.operation_history) > self.max_history:
            self.operation_history = self.operation_history[-self.max_history:]
    
    def get_operation_history(self) -> List[OperationResult]:
        """Get operation history."""
        return self.operation_history.copy()
    
    async def list_directory(self, 
                           directory_path: str, 
                           recursive: bool = False,
                           include_hidden: bool = False) -> List[FileInfo]:
        """
        List directory contents with detailed information.
        
        Args:
            directory_path: Path to directory
            recursive: Whether to list recursively
            include_hidden: Whether to include hidden files
            
        Returns:
            List of FileInfo objects
        """
        try:
            path = Path(directory_path)
            
            if not path.exists() or not path.is_dir():
                return []
            
            files = []
            
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for item in path.glob(pattern):
                # Skip hidden files if not requested
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                file_info = await self.get_file_info(str(item))
                if file_info:
                    files.append(file_info)
            
            return files
            
        except Exception as e:
            logger.error(f"Error listing directory {directory_path}: {e}")
            return []
