2025-07-12 20:28:26,638 - CODY.Providers - ERROR - Failed to initialize provider deepseek: DeepSeek API key is required
2025-07-12 20:28:26,638 - CODY.ProviderManager - WARNING - Failed to initialize DeepSeek provider: DeepSeek API key is required
2025-07-12 20:28:26,638 - CODY.GeminiProvider - INFO - Gemini provider initialized with 3 models
2025-07-12 20:28:26,638 - CODY.Providers - INFO - Provider gemini initialized successfully
2025-07-12 20:28:26,638 - CODY.ProviderManager - INFO - Gemini provider initialized
2025-07-12 20:28:26,639 - CODY.ProviderManager - INFO - Provider manager initialized with 1 providers
2025-07-12 20:28:26,639 - CODY.ProviderManager - INFO - Health monitoring started
2025-07-12 20:28:26,639 - CODY.AdvancedNLP - INFO - Advanced NLP processor initialized
2025-07-12 20:28:26,640 - CODY.IntentA<PERSON>yzer - INFO - Intent analyzer initialized
2025-07-12 20:28:26,641 - CODY.SmartFileOps - INFO - Smart file operations initialized
2025-07-12 20:28:26,641 - CODY.ProjectGenerator - INFO - Project generator initialized
2025-07-12 20:28:26,641 - CODY.MultiTerminal - INFO - Multi-terminal manager initialized with TerminalType.POWERSHELL as default shell
2025-07-12 20:28:26,641 - CODY.BaseAgent - INFO - Initializing CODY v2.0.0
2025-07-12 20:28:26,642 - CODY.BaseAgent - INFO - Registered capability: multi_model_ai
2025-07-12 20:28:26,642 - CODY.BaseAgent - INFO - Registered capability: autonomous_workflows
2025-07-12 20:28:26,642 - CODY.BaseAgent - INFO - Registered capability: predictive_intelligence
2025-07-12 20:28:26,642 - CODY.BaseAgent - INFO - Registered capability: full_stack_projects
2025-07-12 20:28:26,643 - CODY.BaseAgent - INFO - Registered capability: advanced_terminal
2025-07-12 20:28:26,643 - CODY.BaseAgent - INFO - Registered capability: web_integration
2025-07-12 20:28:26,643 - CODY.BaseAgent - INFO - Registered capability: code_intelligence
2025-07-12 20:28:26,643 - CODY.BaseAgent - INFO - Registered capability: natural_language
2025-07-12 20:28:26,643 - CODY.Agent - INFO - AI model providers initialized
2025-07-12 20:28:26,643 - CODY.Agent - INFO - Context engine initialized
2025-07-12 20:28:26,643 - CODY.Agent - INFO - NLP processor initialized
2025-07-12 20:28:26,643 - CODY.Agent - INFO - Workflow engine initialized
2025-07-12 20:28:26,643 - CODY.Agent - INFO - File system tools initialized
2025-07-12 20:28:26,644 - CODY.Agent - INFO - Terminal tools initialized
2025-07-12 20:28:26,644 - CODY.Agent - INFO - Web tools initialized
2025-07-12 20:28:26,644 - CODY.Agent - INFO - Code tools initialized
2025-07-12 20:28:26,644 - CODY.Agent - INFO - CODY core components initialized successfully
2025-07-12 20:28:26,645 - CODY.BaseAgent - INFO - CODY initialized successfully
2025-07-12 20:28:26,645 - CODY.Agent - INFO - CODY v2.0.0 initialized with config: CodyConfig(default_model='deepseek-chat', max_context_length=32000, enable_predictive_prefetching=True, enable_autonomous_workflows=True, enable_multi_terminal=True, enable_web_integration=True, enable_code_intelligence=True, response_timeout=30.0, max_parallel_tasks=5, cache_size_mb=1024, log_level='INFO')
2025-07-12 20:28:26,645 - CODY.AgentFactory - INFO - Created cody agent with ID: cody_ad8b0fbf
2025-07-12 20:28:26,664 - CODY.MultiTerminal - INFO - Created terminal session ccc1e96d-7e4c-4eee-9382-f3d3e6da49f4 (powershell)
2025-07-12 20:28:26,664 - CODY.Main - INFO - CODY v2.0 fully initialized with all components
2025-07-12 20:28:31,841 - CODY.BaseAgent - INFO - Task 75d7135e-965b-459a-bbfa-1fb7c7cde646 submitted for processing
2025-07-12 20:28:55,370 - CODY.BaseAgent - INFO - Task a35ff3db-cc2c-4917-8226-edc433db5463 submitted for processing
2025-07-12 20:29:15,663 - CODY.BaseAgent - INFO - Task bc282374-d9d8-417f-aa24-810b944dc3a9 submitted for processing
2025-07-12 20:30:29,249 - CODY.BaseAgent - INFO - Task 61bc277e-ed76-4da2-8001-57cf161809c4 submitted for processing
2025-07-12 20:30:35,963 - CODY.ProviderManager - INFO - Switched provider from gemini to gemini
2025-07-12 20:30:38,020 - CODY.BaseAgent - INFO - Task 9c3ba76d-b6c2-40b6-ba60-4632007af6bf submitted for processing
2025-07-12 20:51:27,202 - CODY.Providers - ERROR - Failed to initialize provider deepseek: DeepSeek API key is required
2025-07-12 20:51:27,203 - CODY.ProviderManager - WARNING - Failed to initialize DeepSeek provider: DeepSeek API key is required
2025-07-12 20:51:27,203 - CODY.GeminiProvider - INFO - Gemini provider initialized with 3 models
2025-07-12 20:51:27,203 - CODY.Providers - INFO - Provider gemini initialized successfully
2025-07-12 20:51:27,203 - CODY.ProviderManager - INFO - Gemini provider initialized
2025-07-12 20:51:27,203 - CODY.ProviderManager - INFO - Provider manager initialized with 1 providers
2025-07-12 20:51:27,204 - CODY.ProviderManager - INFO - Health monitoring started
2025-07-12 20:51:27,204 - CODY.AdvancedNLP - INFO - Advanced NLP processor initialized
2025-07-12 20:51:27,204 - CODY.IntentAnalyzer - INFO - Intent analyzer initialized
2025-07-12 20:51:27,205 - CODY.SmartFileOps - INFO - Smart file operations initialized
2025-07-12 20:51:27,205 - CODY.ProjectGenerator - INFO - Project generator initialized
2025-07-12 20:51:27,205 - CODY.MultiTerminal - INFO - Multi-terminal manager initialized with TerminalType.POWERSHELL as default shell
2025-07-12 20:51:27,205 - CODY.BaseAgent - INFO - Initializing CODY v2.0.0
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: multi_model_ai
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: autonomous_workflows
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: predictive_intelligence
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: full_stack_projects
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: advanced_terminal
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: web_integration
2025-07-12 20:51:27,206 - CODY.BaseAgent - INFO - Registered capability: code_intelligence
2025-07-12 20:51:27,207 - CODY.BaseAgent - INFO - Registered capability: natural_language
2025-07-12 20:51:27,207 - CODY.Agent - INFO - AI model providers initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - Context engine initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - NLP processor initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - Workflow engine initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - File system tools initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - Terminal tools initialized
2025-07-12 20:51:27,207 - CODY.Agent - INFO - Web tools initialized
2025-07-12 20:51:27,208 - CODY.Agent - INFO - Code tools initialized
2025-07-12 20:51:27,208 - CODY.Agent - INFO - CODY core components initialized successfully
2025-07-12 20:51:27,209 - CODY.BaseAgent - INFO - CODY initialized successfully
2025-07-12 20:51:27,209 - CODY.Agent - INFO - CODY v2.0.0 initialized with config: CodyConfig(default_model='deepseek-chat', max_context_length=32000, enable_predictive_prefetching=True, enable_autonomous_workflows=True, enable_multi_terminal=True, enable_web_integration=True, enable_code_intelligence=True, response_timeout=30.0, max_parallel_tasks=5, cache_size_mb=1024, log_level='INFO')
2025-07-12 20:51:27,209 - CODY.AgentFactory - INFO - Created cody agent with ID: cody_8e45143d
2025-07-12 20:51:27,220 - CODY.MultiTerminal - INFO - Created terminal session 45f44bc4-7cdf-4b4b-8c78-66966a2589c8 (powershell)
2025-07-12 20:51:27,220 - CODY.Main - INFO - CODY v2.0 fully initialized with all components
2025-07-12 20:51:29,988 - CODY.BaseAgent - INFO - Task 96adfd34-0260-4d0f-aa39-911ab668f76d submitted for processing
