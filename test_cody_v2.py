#!/usr/bin/env python3

"""
Test Script for CODY v2.0
Comprehensive testing of the new architecture
"""

import asyncio
import sys
from pathlib import Path

# Add cody_v2 to path
sys.path.insert(0, str(Path(__file__).parent / "cody_v2"))

async def test_core_components():
    """Test core components individually."""
    print("🧪 Testing CODY v2.0 Core Components\n")
    
    # Test 1: Agent Creation
    print("1️⃣ Testing Agent Creation...")
    try:
        from core.agent import create_cody_agent, get_cody_agent, CodyConfig
        
        config = CodyConfig(
            default_model="deepseek-chat",
            enable_predictive_prefetching=True,
            enable_autonomous_workflows=True
        )
        
        agent_id = create_cody_agent(config)
        agent = get_cody_agent(agent_id)
        
        if agent:
            print("   ✅ Agent created successfully")
            print(f"   📊 Agent Status: {agent.get_agent_status()}")
        else:
            print("   ❌ Failed to create agent")
            
    except Exception as e:
        print(f"   ❌ Agent creation failed: {e}")
    
    # Test 2: NLP Processing
    print("\n2️⃣ Testing Advanced NLP...")
    try:
        from core.nlp import AdvancedNLPProcessor
        
        nlp = AdvancedNLPProcessor()
        
        test_inputs = [
            "Create a React app with TypeScript",
            "Debug this Python file",
            "mujhe ek website banani hai React me",
            "Search for best practices in Go programming"
        ]
        
        for text in test_inputs:
            result = await nlp.process(text)
            print(f"   📝 Input: {text}")
            print(f"   🧠 Intent: {result.intent.category.value} ({result.intent.confidence:.2f})")
            print(f"   🌍 Language: {result.language.value}")
            print(f"   ⚡ Processing Time: {result.processing_time:.3f}s")
            print()
            
    except Exception as e:
        print(f"   ❌ NLP testing failed: {e}")
    
    # Test 3: File Operations
    print("3️⃣ Testing Smart File Operations...")
    try:
        from tools.filesystem.smart_file_ops import SmartFileOperations
        
        file_ops = SmartFileOperations()
        
        # Test file creation
        test_file = "test_cody_file.txt"
        result = await file_ops.create_file(test_file, "Hello from CODY v2.0!")
        
        if result.success:
            print("   ✅ File creation successful")
            
            # Test file reading
            success, content, error = await file_ops.read_file(test_file)
            if success:
                print(f"   ✅ File reading successful: {content[:30]}...")
            else:
                print(f"   ❌ File reading failed: {error}")
            
            # Test file deletion
            delete_result = await file_ops.delete_file(test_file)
            if delete_result.success:
                print("   ✅ File deletion successful")
            else:
                print(f"   ❌ File deletion failed: {delete_result.error}")
        else:
            print(f"   ❌ File creation failed: {result.error}")
            
    except Exception as e:
        print(f"   ❌ File operations testing failed: {e}")
    
    # Test 4: Project Generator
    print("\n4️⃣ Testing Project Generator...")
    try:
        from tools.filesystem.project_generator import ProjectGenerator
        
        generator = ProjectGenerator()
        templates = generator.get_available_templates()
        
        print(f"   📋 Available Templates: {len(templates)}")
        for name, desc in list(templates.items())[:3]:
            print(f"      • {name}: {desc}")
        
        print("   ✅ Project generator working")
        
    except Exception as e:
        print(f"   ❌ Project generator testing failed: {e}")
    
    # Test 5: Terminal Manager
    print("\n5️⃣ Testing Multi-Terminal Manager...")
    try:
        from tools.terminal.multi_terminal import MultiTerminalManager

        terminal_manager = MultiTerminalManager()

        # Create a session
        session_id = await terminal_manager.create_session()
        print(f"   ✅ Terminal session created: {session_id[:8]}...")

        # List sessions
        sessions = terminal_manager.list_sessions()
        print(f"   📋 Active sessions: {len(sessions)}")

        # Close session
        closed = await terminal_manager.close_session(session_id)
        if closed:
            print("   ✅ Terminal session closed successfully")

    except Exception as e:
        print(f"   ❌ Terminal manager testing failed: {e}")

    # Test 6: Web Integration
    print("\n6️⃣ Testing Web Integration...")
    try:
        from tools.web import WebSearchEngine, URLFetcher, RAGSystem

        # Test web search
        search_engine = WebSearchEngine()
        print("   ✅ Web search engine initialized")

        # Test URL fetcher
        url_fetcher = URLFetcher()
        test_url = "https://github.com/example/repo"
        content = await url_fetcher.fetch_url(test_url)
        if content:
            print(f"   ✅ URL fetcher working: {content.title}")

        # Test RAG system
        rag_system = RAGSystem(search_engine, url_fetcher)
        print("   ✅ RAG system initialized")

    except Exception as e:
        print(f"   ❌ Web integration testing failed: {e}")

    # Test 7: Code Intelligence
    print("\n7️⃣ Testing Code Intelligence...")
    try:
        from tools.code import CodeAnalyzer, RefactoringEngine

        # Test code analyzer
        analyzer = CodeAnalyzer()
        test_code = '''
def example_function(x, y):
    if x == True:
        return x + y
    else:
        return x - y
'''

        analysis = await analyzer.analyze_code(test_code)
        print(f"   ✅ Code analysis complete: {len(analysis.issues)} issues found")
        print(f"   📊 Metrics: {analysis.metrics.lines_of_code} LOC, complexity: {analysis.metrics.cyclomatic_complexity}")

        # Test refactoring engine
        refactoring_engine = RefactoringEngine(analyzer)
        refactoring_result = await refactoring_engine.refactor_code(test_code)
        print(f"   ✅ Refactoring analysis: {len(refactoring_result.operations)} operations suggested")

    except Exception as e:
        print(f"   ❌ Code intelligence testing failed: {e}")

    # Test 8: Context Management
    print("\n8️⃣ Testing Context Management...")
    try:
        from core.context import ContextManager, ContextType, ContextScope, PredictiveIntelligence

        # Test context manager
        context_manager = ContextManager()

        # Add some context
        context_id = context_manager.add_context(
            ContextType.USER_INTENT,
            ContextScope.SESSION,
            {"intent": "create_project", "language": "python"}
        )
        print(f"   ✅ Context added: {context_id}")

        # Search context
        results = context_manager.search_contexts("python")
        print(f"   🔍 Context search: {len(results)} results")

        # Test predictive intelligence
        predictive_ai = PredictiveIntelligence(context_manager)
        predictions = await predictive_ai.get_predictions()
        print(f"   🔮 Predictions generated: {len(predictions)}")

        # Cleanup
        context_manager.shutdown()
        predictive_ai.shutdown()

    except Exception as e:
        print(f"   ❌ Context management testing failed: {e}")

async def test_integration():
    """Test integrated functionality."""
    print("\n🔗 Testing Integration...\n")
    
    try:
        # Import the main terminal interface
        sys.path.insert(0, str(Path(__file__).parent / "cody_v2"))
        from main import CodyTerminal
        
        # Create terminal instance
        terminal = CodyTerminal()
        
        # Test initialization
        print("🔄 Testing initialization...")
        success = await terminal.initialize()
        
        if success:
            print("✅ Integration test successful - CODY v2.0 is ready!")
            
            # Test a few commands
            test_commands = [
                "/help",
                "/status",
                "/sessions",
                "/projects"
            ]
            
            print("\n🧪 Testing commands...")
            for cmd in test_commands:
                try:
                    response = await terminal.process_input(cmd)
                    print(f"   Command: {cmd}")
                    print(f"   Response: {response[:100]}...")
                    print()
                except Exception as e:
                    print(f"   ❌ Command {cmd} failed: {e}")
        else:
            print("❌ Integration test failed - initialization unsuccessful")
        
        # Cleanup
        if hasattr(terminal, 'shutdown'):
            terminal.shutdown()
            
    except Exception as e:
        print(f"❌ Integration testing failed: {e}")

async def main():
    """Main test function."""
    print("🚀 CODY v2.0 Comprehensive Test Suite")
    print("=" * 50)
    
    # Test core components
    await test_core_components()
    
    # Test integration
    await test_integration()
    
    print("\n" + "=" * 50)
    print("🎯 Test Suite Complete!")
    print("\nIf all tests passed, CODY v2.0 is ready for use!")
    print("Run: python cody_v2/main.py")

if __name__ == "__main__":
    asyncio.run(main())
