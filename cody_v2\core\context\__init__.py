#!/usr/bin/env python3

"""
Context Management for CODY v2.0
Advanced context tracking and predictive intelligence
"""

from .context_manager import (
    ContextManager,
    ContextType,
    ContextScope,
    ContextEntry,
    ContextSnapshot
)

from .predictive_intelligence import (
    PredictiveIntelligence,
    PredictionType,
    Confidence,
    Prediction,
    PredictionPattern
)

__all__ = [
    # Context Management
    'ContextManager',
    'ContextType',
    'ContextScope',
    'ContextEntry',
    'ContextSnapshot',
    
    # Predictive Intelligence
    'PredictiveIntelligence',
    'PredictionType',
    'Confidence',
    'Prediction',
    'PredictionPattern'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Context management and predictive intelligence"
