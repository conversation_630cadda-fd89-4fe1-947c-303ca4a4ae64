#!/usr/bin/env python3

"""
CODY v2.0 - Advanced SaaS AI Terminal Agent
Main entry point for the application
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Add parent directory to path for accessing tools
sys.path.insert(0, str(project_root.parent))

from core.agent import create_cody_agent, get_cody_agent, CodyConfig, TaskRequest
from core.providers import ProviderManager
from core.nlp import AdvancedNLPProcessor, IntentAnalyzer
from tools.filesystem.smart_file_ops import SmartFileOperations
from tools.filesystem.project_generator import ProjectGenerator
from tools.terminal.multi_terminal import MultiTerminalManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cody_v2.log')
    ]
)

logger = logging.getLogger('CODY.Main')

class CodyTerminal:
    """Interactive terminal interface for CODY v2.0."""
    
    def __init__(self):
        """Initialize the terminal interface."""
        self.agent_id = None
        self.agent = None
        self.running = True

        # Initialize core components
        self.provider_manager = None
        self.nlp_processor = None
        self.intent_analyzer = None
        self.file_ops = None
        self.project_generator = None
        self.terminal_manager = None
        
    async def initialize(self):
        """Initialize CODY agent and all components."""
        try:
            print("🔄 Initializing CODY v2.0 components...")

            # Initialize core components
            self.provider_manager = ProviderManager()
            self.nlp_processor = AdvancedNLPProcessor()
            self.intent_analyzer = IntentAnalyzer()
            self.file_ops = SmartFileOperations()
            self.project_generator = ProjectGenerator(self.file_ops)
            self.terminal_manager = MultiTerminalManager()

            print("✅ Core components initialized")

            # Create CODY agent with enhanced configuration
            config = CodyConfig(
                default_model="deepseek-chat",
                enable_predictive_prefetching=True,
                enable_autonomous_workflows=True,
                enable_multi_terminal=True,
                enable_web_integration=True,
                enable_code_intelligence=True,
                max_parallel_tasks=5,
                cache_size_mb=1024
            )

            self.agent_id = create_cody_agent(config)
            self.agent = get_cody_agent(self.agent_id)

            if not self.agent:
                raise Exception("Failed to create CODY agent")

            print("✅ CODY agent initialized")

            # Create default terminal session
            default_session = await self.terminal_manager.create_session()
            print(f"✅ Default terminal session created: {default_session[:8]}...")

            logger.info("CODY v2.0 fully initialized with all components")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize CODY: {e}")
            print(f"❌ Initialization failed: {e}")
            return False
    
    def print_banner(self):
        """Print the CODY banner."""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   ██████╗ ██████╗ ██████╗ ██╗   ██╗    ██╗   ██╗██████╗     ██████╗         ║
║  ██╔════╝██╔═══██╗██╔══██╗╚██╗ ██╔╝    ██║   ██║╚════██╗   ██╔═████╗        ║
║  ██║     ██║   ██║██║  ██║ ╚████╔╝     ██║   ██║ █████╔╝   ██║██╔██║        ║
║  ██║     ██║   ██║██║  ██║  ╚██╔╝      ╚██╗ ██╔╝██╔═══╝    ████╔╝██║        ║
║  ╚██████╗╚██████╔╝██████╔╝   ██║        ╚████╔╝ ███████╗██╗╚██████╔╝        ║
║   ╚═════╝ ╚═════╝ ╚═════╝    ╚═╝         ╚═══╝  ╚══════╝╚═╝ ╚═════╝         ║
║                                                                              ║
║                    Advanced SaaS AI Terminal Agent                          ║
║                                                                              ║
║  🚀 Multi-Model AI Support  🧠 Autonomous Workflows  🔧 Full-Stack Projects ║
║  🌐 Web Integration         📁 Smart File Operations  ⚡ Predictive AI       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

Welcome to CODY v2.0 - Your Advanced AI Coding Assistant!

🚀 Available Commands:
  /switch <model>     - Switch AI model (gemini, deepseek, deepseek-reasoner)
  /sessions          - List active terminal sessions
  /projects          - Show available project templates
  /help              - Show this help information
  /status            - Show agent status and metrics
  /config            - Show current configuration
  /exit or /quit     - Exit CODY

💡 Natural Language Examples:
  • "Create a React app with TypeScript and Tailwind CSS"
  • "Debug this Python file and fix any issues"
  • "Search for best practices in Go programming"
  • "Run npm install in the terminal"
  • "List files in the current directory"
  • "Refactor this code to use async/await patterns"

🎯 Advanced Features:
  • Multi-model AI switching (Gemini ⚡ DeepSeek)
  • Multi-terminal session management
  • Full-stack project generation
  • Intelligent file operations with backup
  • Context-aware code analysis
  • Predictive command suggestions

Let's build something amazing together! 🚀
"""
        print(banner)
    
    def print_status(self):
        """Print current agent status."""
        if self.agent:
            status = self.agent.get_agent_status()
            print(f"\n📊 CODY Status:")
            print(f"   State: {status['state']}")
            print(f"   Current Model: {self.agent.get_current_model()}")
            print(f"   Active Tasks: {status['active_tasks']}")
            print(f"   Completed Tasks: {status['completed_tasks']}")
            print(f"   Capabilities: {len(status['capabilities'])}")
            
            if status.get('metrics'):
                metrics = status['metrics']
                print(f"   Tasks Processed: {metrics['tasks_processed']}")
                print(f"   Success Rate: {metrics['tasks_successful']}/{metrics['tasks_processed']}")
                if metrics['average_response_time'] > 0:
                    print(f"   Avg Response Time: {metrics['average_response_time']:.2f}s")
    
    async def process_input(self, user_input: str) -> str:
        """Process user input with advanced NLP and intelligent routing."""
        if not self.agent:
            return "❌ CODY agent not initialized"

        try:
            # Process with advanced NLP
            nlp_result = await self.nlp_processor.process(user_input)

            # Analyze intent
            intent_analysis = await self.intent_analyzer.analyze_intent(nlp_result.intent)

            # Handle different types of requests
            if user_input.startswith('/'):
                return await self._handle_system_command(user_input)
            elif intent_analysis.original_intent.category.value == "project_management":
                return await self._handle_project_request(user_input, intent_analysis)
            elif intent_analysis.original_intent.category.value == "terminal_commands":
                return await self._handle_terminal_request(user_input, intent_analysis)
            elif intent_analysis.original_intent.category.value == "file_operations":
                return await self._handle_file_request(user_input, intent_analysis)
            else:
                # Use the agent for general processing
                return await self._handle_general_request(user_input, nlp_result, intent_analysis)

        except Exception as e:
            logger.error(f"Error processing input: {e}")
            return f"❌ Error: {str(e)}"

    async def _handle_system_command(self, command: str) -> str:
        """Handle system commands like /switch, /help, etc."""
        parts = command.split()
        cmd = parts[0].lower()

        if cmd == "/switch" and len(parts) > 1:
            model = parts[1].lower()
            if self.provider_manager.switch_provider(model):
                return f"✅ Switched to {model} model"
            else:
                return f"❌ Failed to switch to {model}. Available: gemini, deepseek"

        elif cmd == "/sessions":
            sessions = self.terminal_manager.list_sessions()
            if not sessions:
                return "📋 No active terminal sessions"

            response = "📋 Active Terminal Sessions:\n"
            for session in sessions:
                response += f"  • {session['session_id'][:8]}... ({session['terminal_type']}) - {session['status']}\n"
            return response

        elif cmd == "/projects":
            templates = self.project_generator.get_available_templates()
            response = "🏗️ Available Project Templates:\n"
            for name, desc in templates.items():
                response += f"  • {name}: {desc}\n"
            return response

        elif cmd == "/help":
            return self._get_help_text()

        else:
            # Pass to agent for handling
            task = TaskRequest(user_input=command, task_type="system_command")
            task_id = await self.agent.submit_task(task)
            await asyncio.sleep(0.1)

            result = self.agent.get_task_result(task_id)
            if result and result.status.value == "completed":
                return f"✅ {result.result}"
            else:
                return f"❌ Unknown command: {command}"

    async def _handle_project_request(self, user_input: str, intent_analysis) -> str:
        """Handle project creation and management requests."""
        # Simple project creation detection
        if "create" in user_input.lower() and any(word in user_input.lower() for word in ["app", "project", "website"]):
            return "🏗️ Project creation detected! Use '/projects' to see available templates.\n" + \
                   "Example: 'Create a React app called MyApp' (feature coming soon)"

        return "🏗️ Project management feature detected. Full implementation coming soon!"

    async def _handle_terminal_request(self, user_input: str, intent_analysis) -> str:
        """Handle terminal command requests."""
        # Extract command from natural language
        command = user_input.strip()

        # Get first available session
        sessions = self.terminal_manager.list_sessions()
        if not sessions:
            session_id = await self.terminal_manager.create_session()
        else:
            session_id = sessions[0]['session_id']

        try:
            result = await self.terminal_manager.execute_command(session_id, command)

            response = f"💻 Terminal Output:\n"
            response += f"Command: {result.command}\n"
            response += f"Exit Code: {result.exit_code}\n"

            if result.stdout:
                response += f"Output:\n{result.stdout}\n"

            if result.stderr:
                response += f"Error:\n{result.stderr}\n"

            response += f"Execution Time: {result.execution_time:.2f}s"

            return response

        except Exception as e:
            return f"❌ Terminal execution failed: {e}"

    async def _handle_file_request(self, user_input: str, intent_analysis) -> str:
        """Handle file operation requests."""
        return "📁 File operation detected. Advanced file handling coming soon!"

    async def _handle_general_request(self, user_input: str, nlp_result, intent_analysis) -> str:
        """Handle general requests using the agent."""
        # Create enhanced task request with NLP context
        task = TaskRequest(
            user_input=user_input,
            task_type="general_request",
            context={
                "nlp_result": nlp_result.__dict__,
                "intent_analysis": intent_analysis.__dict__,
                "language": nlp_result.language.value,
                "complexity": nlp_result.intent.complexity.value
            }
        )

        # Submit task
        task_id = await self.agent.submit_task(task)

        # Wait for result
        await asyncio.sleep(0.1)

        result = self.agent.get_task_result(task_id)
        if result:
            if result.status.value == "completed":
                return f"🤖 {result.result}"
            elif result.status.value == "failed":
                return f"❌ Error: {result.error}"
            else:
                return f"⏳ Processing... (Status: {result.status.value})"
        else:
            return "⏳ Task is being processed..."

    def _get_help_text(self) -> str:
        """Get comprehensive help text."""
        return """
🤖 CODY v2.0 - Advanced SaaS AI Terminal Agent Help

🚀 SYSTEM COMMANDS:
  /switch <model>     - Switch AI model (gemini, deepseek, deepseek-reasoner)
  /sessions          - List active terminal sessions
  /projects          - Show available project templates
  /status            - Show agent status and metrics
  /config            - Show current configuration
  /help              - Show this help information
  /exit or /quit     - Exit CODY

💻 TERMINAL OPERATIONS:
  • "Run npm install"
  • "Execute python script.py"
  • "List directory contents"
  • "Check git status"
  • "Install package with pip"

🏗️ PROJECT GENERATION:
  • "Create a React app"
  • "Generate FastAPI backend"
  • "Setup full-stack project"
  • "Create data science project"

📁 FILE OPERATIONS:
  • "Create a new file"
  • "Read file contents"
  • "Delete old files"
  • "Copy project files"

🧠 CODE ASSISTANCE:
  • "Debug this Python code"
  • "Refactor JavaScript function"
  • "Explain this algorithm"
  • "Generate unit tests"
  • "Optimize SQL query"

🌐 WEB & SEARCH:
  • "Search for React best practices"
  • "Find documentation for FastAPI"
  • "Look up Python tutorials"

🎯 ADVANCED FEATURES:
  ✅ Multi-language support (English, Hindi, Mixed)
  ✅ Context-aware understanding
  ✅ Predictive intelligence
  ✅ Autonomous workflows
  ✅ Multi-terminal management
  ✅ Smart file operations with backup
  ✅ Full-stack project scaffolding
  ✅ Real-time error detection and fixing

💡 TIP: Just type naturally! CODY understands context and can handle complex requests.
"""
    
    async def run(self):
        """Run the interactive terminal."""
        self.print_banner()
        
        if not await self.initialize():
            print("❌ Failed to initialize CODY. Please check your configuration.")
            return
        
        print("🎯 CODY is ready! Type your request or command:")
        
        while self.running:
            try:
                # Get user input
                user_input = input("\n🤖 CODY> ").strip()
                
                if not user_input:
                    continue
                
                # Handle exit commands
                if user_input.lower() in ['/exit', '/quit', 'exit', 'quit']:
                    print("👋 Goodbye! Thanks for using CODY v2.0")
                    self.running = False
                    break
                
                # Handle status command
                elif user_input.lower() == '/status':
                    self.print_status()
                    continue
                
                # Process the input
                print("🔄 Processing...")
                response = await self.process_input(user_input)
                print(f"\n{response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using CODY v2.0")
                self.running = False
                break
            except EOFError:
                print("\n\n👋 Goodbye! Thanks for using CODY v2.0")
                self.running = False
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                print(f"❌ Unexpected error: {e}")
    
    def shutdown(self):
        """Shutdown CODY gracefully."""
        if self.agent:
            try:
                self.agent.shutdown()
                logger.info("CODY agent shutdown complete")
            except Exception as e:
                logger.error(f"Error during shutdown: {e}")

async def main():
    """Main entry point."""
    terminal = CodyTerminal()
    
    try:
        await terminal.run()
    finally:
        terminal.shutdown()

if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ CODY v2.0 requires Python 3.8 or higher")
        sys.exit(1)
    
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
