#!/usr/bin/env python3

"""
Gemini AI Provider for CODY v2.0
Implementation for Google Gemini models
"""

import asyncio
import json
import logging
import os
from typing import AsyncGenerator, Dict, List, Optional
import time

from .base_provider import (
    BaseAIProvider,
    ProviderConfig,
    ModelInfo,
    ModelType,
    ChatRequest,
    ChatResponse,
    ChatMessage
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('CODY.GeminiProvider')

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Google Generative AI library not available")

class GeminiProvider(BaseAIProvider):
    """
    Google Gemini AI provider implementation.
    Supports Gemini 2.0 Flash and other Gemini models.
    """
    
    def __init__(self, config: Optional[ProviderConfig] = None):
        """
        Initialize Gemini provider.
        
        Args:
            config: Provider configuration
        """
        if not GEMINI_AVAILABLE:
            raise ImportError("Google Generative AI library is required for Gemini provider")
        
        if config is None:
            config = ProviderConfig(
                api_key=os.getenv('GEMINI_API_KEY', ''),
                timeout=30.0,
                max_retries=3,
                rate_limit_per_minute=60
            )
        
        super().__init__("gemini", config)
        self.client = None
    
    def initialize(self) -> None:
        """Initialize Gemini-specific components."""
        if not self.config.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.config.api_key)
        
        # Register available models
        self.models = {
            "gemini-2.0-flash": ModelInfo(
                name="gemini-2.0-flash",
                display_name="Gemini 2.0 Flash",
                model_type=ModelType.CHAT,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=True,
                supports_vision=True,
                cost_per_token=0.00015,
                description="Google's latest and fastest Gemini model with multimodal capabilities"
            ),
            "gemini-1.5-flash": ModelInfo(
                name="gemini-1.5-flash",
                display_name="Gemini 1.5 Flash",
                model_type=ModelType.CHAT,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=True,
                supports_vision=True,
                cost_per_token=0.00015,
                description="Fast and efficient Gemini model with multimodal capabilities"
            ),
            "gemini-2.5-flash": ModelInfo(
                name="gemini-2.5-flash",
                display_name="Gemini 2.5 Flash",
                model_type=ModelType.CHAT,
                max_tokens=32768,
                supports_streaming=True,
                supports_function_calling=True,
                supports_vision=True,
                cost_per_token=0.0035,
                description="Most capable Gemini model with advanced reasoning"
            )
        }
        
        logger.info(f"Gemini provider initialized with {len(self.models)} models")
    
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion using Gemini API.
        
        Args:
            request: Chat completion request
            
        Returns:
            Chat completion response
        """
        return await self._execute_with_retry(self._chat_completion_impl, request)
    
    async def _chat_completion_impl(self, request: ChatRequest) -> ChatResponse:
        """Internal implementation of chat completion."""
        # Validate request
        if not self.validate_request(request):
            raise ValueError("Invalid chat request")
        
        # Optimize request for Gemini
        optimized_request = self.optimize_request(request)
        
        # Create Gemini model instance
        model = genai.GenerativeModel(
            model_name=optimized_request.model,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        # Convert messages to Gemini format
        gemini_messages = self._convert_messages_to_gemini(optimized_request.messages)
        
        # Generate configuration
        generation_config = genai.types.GenerationConfig(
            temperature=optimized_request.temperature,
            top_p=optimized_request.top_p,
            max_output_tokens=optimized_request.max_tokens,
            stop_sequences=optimized_request.stop
        )
        
        start_time = time.time()
        
        try:
            logger.info(f"Calling Gemini with prompt: {gemini_messages}")
            # Generate response
            response = await asyncio.to_thread(
                model.generate_content,
                gemini_messages,
                generation_config=generation_config
            )
            
            response_time = time.time() - start_time
            
            # Extract response content
            content = response.text if response.text else ""
            
            # Calculate token usage (Gemini doesn't provide exact counts)
            prompt_tokens = sum(len(msg.content.split()) for msg in optimized_request.messages)
            completion_tokens = len(content.split())
            
            logger.info(f"Gemini response: {response.text}")

            return ChatResponse(
                content=content,
                model=optimized_request.model,
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens
                },
                finish_reason=self._get_finish_reason(response),
                response_time=response_time,
                metadata={
                    "provider": "gemini",
                    "safety_ratings": getattr(response, 'safety_ratings', []),
                    "prompt_feedback": getattr(response, 'prompt_feedback', None)
                }
            )
            
        except Exception as e:
            logger.error(f"Gemini API error: {e}", exc_info=True)
            raise
    
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion.
        
        Args:
            request: Chat completion request
            
        Yields:
            Partial response chunks
        """
        # Validate request
        if not self.validate_request(request):
            raise ValueError("Invalid chat request")
        
        # Check rate limit
        if not self._check_rate_limit():
            raise Exception("Rate limit exceeded")
        
        self._record_request()
        
        # Optimize request for Gemini
        optimized_request = self.optimize_request(request)
        
        # Create Gemini model instance
        model = genai.GenerativeModel(
            model_name=optimized_request.model,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        # Convert messages to Gemini format
        gemini_messages = self._convert_messages_to_gemini(optimized_request.messages)
        
        # Generate configuration
        generation_config = genai.types.GenerationConfig(
            temperature=optimized_request.temperature,
            top_p=optimized_request.top_p,
            max_output_tokens=optimized_request.max_tokens,
            stop_sequences=optimized_request.stop
        )
        
        try:
            # Generate streaming response
            response = model.generate_content(
                gemini_messages,
                generation_config=generation_config,
                stream=True
            )
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
            
            self._record_success(0.0)  # Response time will be calculated elsewhere
            
        except Exception as e:
            self._record_failure()
            logger.error(f"Gemini streaming error: {e}")
            raise
    
    def _convert_messages_to_gemini(self, messages: List[ChatMessage]) -> List[Dict[str, str]]:
        """Convert chat messages to Gemini format."""
        gemini_messages = []
        
        for msg in messages:
            if msg.role == "system":
                # Gemini doesn't have a system role, so we prepend to the first user message
                if gemini_messages and gemini_messages[-1]["role"] == "user":
                    gemini_messages[-1]["parts"][0]["text"] = f"{msg.content}\n\n{gemini_messages[-1]['parts'][0]['text']}"
                else:
                    gemini_messages.append({
                        "role": "user",
                        "parts": [{"text": msg.content}]
                    })
            elif msg.role == "user":
                gemini_messages.append({
                    "role": "user",
                    "parts": [{"text": msg.content}]
                })
            elif msg.role == "assistant":
                gemini_messages.append({
                    "role": "model",
                    "parts": [{"text": msg.content}]
                })
        
        return gemini_messages
    
    def _get_finish_reason(self, response) -> str:
        """Extract finish reason from Gemini response."""
        if hasattr(response, 'candidates') and response.candidates:
            candidate = response.candidates[0]
            if hasattr(candidate, 'finish_reason'):
                return str(candidate.finish_reason)
        return "stop"
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get list of available Gemini models."""
        return list(self.models.values())
    
    def optimize_request(self, request: ChatRequest) -> ChatRequest:
        """Optimize request for Gemini models."""
        optimized = ChatRequest(
            messages=request.messages.copy(),
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stream=request.stream,
            stop=request.stop,
            metadata=request.metadata.copy()
        )
        
        # Gemini-specific optimizations
        if "2.0" in request.model:
            # For Gemini 2.0, we can use higher token limits
            if not optimized.max_tokens:
                optimized.max_tokens = 8192
        
        # Ensure temperature is within Gemini's range (0.0 to 2.0)
        optimized.temperature = max(0.0, min(2.0, optimized.temperature))
        
        # Ensure top_p is within range (0.0 to 1.0)
        optimized.top_p = max(0.0, min(1.0, optimized.top_p))
        
        return optimized
    
    def validate_request(self, request: ChatRequest) -> bool:
        """Validate request for Gemini models."""
        if not super().validate_request(request):
            return False
        
        # Gemini-specific validations
        if request.model not in self.models:
            logger.warning(f"Unknown Gemini model: {request.model}")
            return False
        
        # Check for empty messages
        for msg in request.messages:
            if not msg.content.strip():
                logger.warning("Empty message content detected")
                return False
        
        return True
