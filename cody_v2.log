2025-07-12 20:26:31,911 - CODY.Providers - ERROR - Failed to initialize provider deepseek: DeepSeek API key is required
2025-07-12 20:26:31,911 - CODY.ProviderManager - WARNING - Failed to initialize DeepSeek provider: DeepSeek API key is required
2025-07-12 20:26:31,911 - CODY.Gemini<PERSON><PERSON>ider - INFO - Gemini provider initialized with 3 models
2025-07-12 20:26:31,911 - CODY.Providers - INFO - Provider gemini initialized successfully
2025-07-12 20:26:31,911 - CODY.ProviderManager - INFO - Gemini provider initialized
2025-07-12 20:26:31,911 - CODY.ProviderManager - INFO - Provider manager initialized with 1 providers
2025-07-12 20:26:31,912 - CODY.ProviderManager - INFO - Health monitoring started
2025-07-12 20:26:31,912 - CODY.AdvancedNLP - INFO - Advanced NLP processor initialized
2025-07-12 20:26:31,912 - CODY.IntentAnalyzer - INFO - Intent analyzer initialized
2025-07-12 20:26:31,912 - CODY.SmartFileOps - INFO - Smart file operations initialized
2025-07-12 20:26:31,913 - CODY.ProjectGenerator - INFO - Project generator initialized
2025-07-12 20:26:31,913 - CODY.MultiTerminal - INFO - Multi-terminal manager initialized with TerminalType.POWERSHELL as default shell
2025-07-12 20:26:31,913 - CODY.BaseAgent - INFO - Initializing CODY v2.0.0
2025-07-12 20:26:31,913 - CODY.BaseAgent - INFO - Registered capability: multi_model_ai
2025-07-12 20:26:31,913 - CODY.BaseAgent - INFO - Registered capability: autonomous_workflows
2025-07-12 20:26:31,913 - CODY.BaseAgent - INFO - Registered capability: predictive_intelligence
2025-07-12 20:26:31,914 - CODY.BaseAgent - INFO - Registered capability: full_stack_projects
2025-07-12 20:26:31,914 - CODY.BaseAgent - INFO - Registered capability: advanced_terminal
2025-07-12 20:26:31,914 - CODY.BaseAgent - INFO - Registered capability: web_integration
2025-07-12 20:26:31,914 - CODY.BaseAgent - INFO - Registered capability: code_intelligence
2025-07-12 20:26:31,914 - CODY.BaseAgent - INFO - Registered capability: natural_language
2025-07-12 20:26:31,914 - CODY.Agent - INFO - AI model providers initialized
2025-07-12 20:26:31,915 - CODY.Agent - INFO - Context engine initialized
2025-07-12 20:26:31,915 - CODY.Agent - INFO - NLP processor initialized
2025-07-12 20:26:31,915 - CODY.Agent - INFO - Workflow engine initialized
2025-07-12 20:26:31,916 - CODY.Agent - INFO - File system tools initialized
2025-07-12 20:26:31,916 - CODY.Agent - INFO - Terminal tools initialized
2025-07-12 20:26:31,916 - CODY.Agent - INFO - Web tools initialized
2025-07-12 20:26:31,916 - CODY.Agent - INFO - Code tools initialized
2025-07-12 20:26:31,916 - CODY.Agent - INFO - CODY core components initialized successfully
2025-07-12 20:26:31,917 - CODY.BaseAgent - INFO - CODY initialized successfully
2025-07-12 20:26:31,917 - CODY.Agent - INFO - CODY v2.0.0 initialized with config: CodyConfig(default_model='deepseek-chat', max_context_length=32000, enable_predictive_prefetching=True, enable_autonomous_workflows=True, enable_multi_terminal=True, enable_web_integration=True, enable_code_intelligence=True, response_timeout=30.0, max_parallel_tasks=5, cache_size_mb=1024, log_level='INFO')
2025-07-12 20:26:31,917 - CODY.AgentFactory - INFO - Created cody agent with ID: cody_22382aed
2025-07-12 20:26:31,921 - CODY.MultiTerminal - INFO - Created terminal session c38984f5-6c83-42ad-98a6-1d582122d055 (powershell)
2025-07-12 20:26:31,922 - CODY.Main - INFO - CODY v2.0 fully initialized with all components
2025-07-12 20:26:31,926 - CODY.BaseAgent - INFO - Task 71988e4e-7871-4b69-8fbf-fc7db6397dc3 submitted for processing
2025-07-12 20:26:32,043 - CODY.BaseAgent - INFO - Shutting down CODY
2025-07-12 20:26:32,043 - CODY.BaseAgent - INFO - CODY shutdown complete
2025-07-12 20:26:32,044 - CODY.Main - INFO - CODY agent shutdown complete
2025-07-12 20:51:03,567 - CODY.Providers - ERROR - Failed to initialize provider deepseek: DeepSeek API key is required
2025-07-12 20:51:03,567 - CODY.ProviderManager - WARNING - Failed to initialize DeepSeek provider: DeepSeek API key is required
2025-07-12 20:51:03,567 - CODY.GeminiProvider - INFO - Gemini provider initialized with 3 models
2025-07-12 20:51:03,569 - CODY.Providers - INFO - Provider gemini initialized successfully
2025-07-12 20:51:03,569 - CODY.ProviderManager - INFO - Gemini provider initialized
2025-07-12 20:51:03,569 - CODY.ProviderManager - INFO - Provider manager initialized with 1 providers
2025-07-12 20:51:03,570 - CODY.ProviderManager - INFO - Health monitoring started
2025-07-12 20:51:03,570 - CODY.AdvancedNLP - INFO - Advanced NLP processor initialized
2025-07-12 20:51:03,570 - CODY.IntentAnalyzer - INFO - Intent analyzer initialized
2025-07-12 20:51:03,571 - CODY.SmartFileOps - INFO - Smart file operations initialized
2025-07-12 20:51:03,571 - CODY.ProjectGenerator - INFO - Project generator initialized
2025-07-12 20:51:03,576 - CODY.MultiTerminal - INFO - Multi-terminal manager initialized with TerminalType.POWERSHELL as default shell
2025-07-12 20:51:03,579 - CODY.BaseAgent - INFO - Initializing CODY v2.0.0
2025-07-12 20:51:03,581 - CODY.BaseAgent - INFO - Registered capability: multi_model_ai
2025-07-12 20:51:03,581 - CODY.BaseAgent - INFO - Registered capability: autonomous_workflows
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: predictive_intelligence
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: full_stack_projects
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: advanced_terminal
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: web_integration
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: code_intelligence
2025-07-12 20:51:03,582 - CODY.BaseAgent - INFO - Registered capability: natural_language
2025-07-12 20:51:03,583 - CODY.Agent - INFO - AI model providers initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - Context engine initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - NLP processor initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - Workflow engine initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - File system tools initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - Terminal tools initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - Web tools initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - Code tools initialized
2025-07-12 20:51:03,583 - CODY.Agent - INFO - CODY core components initialized successfully
2025-07-12 20:51:03,585 - CODY.BaseAgent - INFO - CODY initialized successfully
2025-07-12 20:51:03,585 - CODY.Agent - INFO - CODY v2.0.0 initialized with config: CodyConfig(default_model='deepseek-chat', max_context_length=32000, enable_predictive_prefetching=True, enable_autonomous_workflows=True, enable_multi_terminal=True, enable_web_integration=True, enable_code_intelligence=True, response_timeout=30.0, max_parallel_tasks=5, cache_size_mb=1024, log_level='INFO')
2025-07-12 20:51:03,585 - CODY.AgentFactory - INFO - Created cody agent with ID: cody_6aa3c32f
2025-07-12 20:51:03,589 - CODY.MultiTerminal - INFO - Created terminal session 0044797a-ad30-4a7c-a89e-2d9c95c39b3f (powershell)
2025-07-12 20:51:03,590 - CODY.Main - INFO - CODY v2.0 fully initialized with all components
2025-07-12 20:51:03,594 - CODY.BaseAgent - INFO - Task 9e875950-7b9e-4874-80c4-ea6267b3f6ad submitted for processing
2025-07-12 20:51:03,702 - CODY.BaseAgent - INFO - Shutting down CODY
2025-07-12 20:51:03,702 - CODY.BaseAgent - INFO - CODY shutdown complete
2025-07-12 20:51:03,702 - CODY.Main - INFO - CODY agent shutdown complete
