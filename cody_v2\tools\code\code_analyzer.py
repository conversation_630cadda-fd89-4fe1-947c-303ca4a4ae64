#!/usr/bin/env python3

"""
Code Analyzer for CODY v2.0
Advanced code analysis with AST parsing, pattern detection, and quality assessment
"""

import ast
import logging
import re
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

logger = logging.getLogger('CODY.CodeAnalyzer')

class CodeLanguage(Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    GO = "go"
    RUST = "rust"
    CPP = "cpp"
    CSHARP = "csharp"
    PHP = "php"
    RUBY = "ruby"
    UNKNOWN = "unknown"

class IssueType(Enum):
    """Types of code issues."""
    SYNTAX_ERROR = "syntax_error"
    LOGIC_ERROR = "logic_error"
    PERFORMANCE = "performance"
    SECURITY = "security"
    STYLE = "style"
    COMPLEXITY = "complexity"
    MAINTAINABILITY = "maintainability"
    DOCUMENTATION = "documentation"
    TESTING = "testing"

class Severity(Enum):
    """Issue severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

@dataclass
class CodeIssue:
    """Represents a code issue."""
    issue_type: IssueType
    severity: Severity
    message: str
    line_number: int
    column: int = 0
    file_path: str = ""
    suggestion: str = ""
    code_snippet: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CodeMetrics:
    """Code quality metrics."""
    lines_of_code: int
    cyclomatic_complexity: int
    maintainability_index: float
    test_coverage: float
    code_duplication: float
    technical_debt_ratio: float
    documentation_coverage: float
    security_score: float

@dataclass
class FunctionInfo:
    """Information about a function."""
    name: str
    line_start: int
    line_end: int
    parameters: List[str]
    return_type: Optional[str]
    complexity: int
    docstring: Optional[str]
    is_async: bool = False
    decorators: List[str] = field(default_factory=list)

@dataclass
class ClassInfo:
    """Information about a class."""
    name: str
    line_start: int
    line_end: int
    methods: List[FunctionInfo]
    attributes: List[str]
    inheritance: List[str]
    docstring: Optional[str]
    is_abstract: bool = False

@dataclass
class CodeAnalysisResult:
    """Complete code analysis result."""
    file_path: str
    language: CodeLanguage
    metrics: CodeMetrics
    issues: List[CodeIssue]
    functions: List[FunctionInfo]
    classes: List[ClassInfo]
    imports: List[str]
    dependencies: List[str]
    analysis_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class CodeAnalyzer:
    """
    Advanced code analyzer with multi-language support.
    
    Features:
    - AST-based analysis for deep code understanding
    - Quality metrics calculation
    - Issue detection and suggestions
    - Performance analysis
    - Security vulnerability detection
    - Code complexity assessment
    """
    
    def __init__(self):
        """Initialize the code analyzer."""
        self.language_detectors = self._initialize_language_detectors()
        self.analyzers = self._initialize_analyzers()
        self.issue_patterns = self._initialize_issue_patterns()
        
        logger.info("Code analyzer initialized")
    
    def _initialize_language_detectors(self) -> Dict[str, CodeLanguage]:
        """Initialize file extension to language mappings."""
        return {
            '.py': CodeLanguage.PYTHON,
            '.js': CodeLanguage.JAVASCRIPT,
            '.jsx': CodeLanguage.JAVASCRIPT,
            '.ts': CodeLanguage.TYPESCRIPT,
            '.tsx': CodeLanguage.TYPESCRIPT,
            '.java': CodeLanguage.JAVA,
            '.go': CodeLanguage.GO,
            '.rs': CodeLanguage.RUST,
            '.cpp': CodeLanguage.CPP,
            '.cc': CodeLanguage.CPP,
            '.cxx': CodeLanguage.CPP,
            '.c': CodeLanguage.CPP,
            '.h': CodeLanguage.CPP,
            '.hpp': CodeLanguage.CPP,
            '.cs': CodeLanguage.CSHARP,
            '.php': CodeLanguage.PHP,
            '.rb': CodeLanguage.RUBY
        }
    
    def _initialize_analyzers(self) -> Dict[CodeLanguage, callable]:
        """Initialize language-specific analyzers."""
        return {
            CodeLanguage.PYTHON: self._analyze_python,
            CodeLanguage.JAVASCRIPT: self._analyze_javascript,
            CodeLanguage.TYPESCRIPT: self._analyze_typescript,
            CodeLanguage.JAVA: self._analyze_java,
            CodeLanguage.GO: self._analyze_go,
            CodeLanguage.RUST: self._analyze_rust,
            CodeLanguage.CPP: self._analyze_cpp,
            CodeLanguage.CSHARP: self._analyze_csharp,
            CodeLanguage.PHP: self._analyze_php,
            CodeLanguage.RUBY: self._analyze_ruby
        }
    
    def _initialize_issue_patterns(self) -> Dict[CodeLanguage, Dict[IssueType, List[Dict[str, Any]]]]:
        """Initialize issue detection patterns for each language."""
        return {
            CodeLanguage.PYTHON: {
                IssueType.SECURITY: [
                    {
                        "pattern": r"eval\s*\(",
                        "message": "Use of eval() can be dangerous",
                        "severity": Severity.HIGH,
                        "suggestion": "Consider using ast.literal_eval() or safer alternatives"
                    },
                    {
                        "pattern": r"exec\s*\(",
                        "message": "Use of exec() can be dangerous",
                        "severity": Severity.HIGH,
                        "suggestion": "Avoid dynamic code execution"
                    }
                ],
                IssueType.PERFORMANCE: [
                    {
                        "pattern": r"for\s+\w+\s+in\s+range\s*\(\s*len\s*\(",
                        "message": "Inefficient iteration pattern",
                        "severity": Severity.MEDIUM,
                        "suggestion": "Use enumerate() or iterate directly over the sequence"
                    }
                ],
                IssueType.STYLE: [
                    {
                        "pattern": r"^\s*import\s+\*",
                        "message": "Wildcard imports should be avoided",
                        "severity": Severity.LOW,
                        "suggestion": "Import specific names or use qualified imports"
                    }
                ]
            },
            CodeLanguage.JAVASCRIPT: {
                IssueType.SECURITY: [
                    {
                        "pattern": r"eval\s*\(",
                        "message": "Use of eval() is dangerous",
                        "severity": Severity.HIGH,
                        "suggestion": "Use JSON.parse() or safer alternatives"
                    }
                ],
                IssueType.PERFORMANCE: [
                    {
                        "pattern": r"document\.getElementById",
                        "message": "Frequent DOM queries can be slow",
                        "severity": Severity.MEDIUM,
                        "suggestion": "Cache DOM references"
                    }
                ]
            }
        }
    
    async def analyze_code(self, code: str, file_path: str = "") -> CodeAnalysisResult:
        """
        Analyze code and return comprehensive analysis.
        
        Args:
            code: Source code to analyze
            file_path: Optional file path for context
            
        Returns:
            Complete analysis result
        """
        start_time = time.time()
        
        try:
            # Detect language
            language = self._detect_language(code, file_path)
            
            # Get appropriate analyzer
            analyzer = self.analyzers.get(language, self._analyze_generic)
            
            # Perform analysis
            result = await analyzer(code, file_path)
            result.analysis_time = time.time() - start_time
            
            return result
            
        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            
            # Return basic result on error
            return CodeAnalysisResult(
                file_path=file_path,
                language=CodeLanguage.UNKNOWN,
                metrics=CodeMetrics(
                    lines_of_code=len(code.splitlines()),
                    cyclomatic_complexity=1,
                    maintainability_index=50.0,
                    test_coverage=0.0,
                    code_duplication=0.0,
                    technical_debt_ratio=0.0,
                    documentation_coverage=0.0,
                    security_score=50.0
                ),
                issues=[],
                functions=[],
                classes=[],
                imports=[],
                dependencies=[],
                analysis_time=time.time() - start_time,
                metadata={"error": str(e)}
            )
    
    def _detect_language(self, code: str, file_path: str) -> CodeLanguage:
        """Detect programming language from code and file path."""
        if file_path:
            extension = Path(file_path).suffix.lower()
            if extension in self.language_detectors:
                return self.language_detectors[extension]
        
        # Content-based detection
        if re.search(r'def\s+\w+\s*\(', code) and 'import' in code:
            return CodeLanguage.PYTHON
        elif re.search(r'function\s+\w+\s*\(', code) or 'console.log' in code:
            return CodeLanguage.JAVASCRIPT
        elif 'public class' in code and 'public static void main' in code:
            return CodeLanguage.JAVA
        elif 'func ' in code and 'package ' in code:
            return CodeLanguage.GO
        elif 'fn ' in code and 'use ' in code:
            return CodeLanguage.RUST
        
        return CodeLanguage.UNKNOWN
    
    async def _analyze_python(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze Python code using AST."""
        try:
            # Parse AST
            tree = ast.parse(code)
            
            # Extract information
            functions = self._extract_python_functions(tree, code)
            classes = self._extract_python_classes(tree, code)
            imports = self._extract_python_imports(tree)
            
            # Calculate metrics
            metrics = self._calculate_python_metrics(code, tree)
            
            # Detect issues
            issues = self._detect_python_issues(code, tree)
            
            return CodeAnalysisResult(
                file_path=file_path,
                language=CodeLanguage.PYTHON,
                metrics=metrics,
                issues=issues,
                functions=functions,
                classes=classes,
                imports=imports,
                dependencies=self._extract_dependencies(imports),
                analysis_time=0.0,  # Will be set by caller
                metadata={"ast_nodes": len(list(ast.walk(tree)))}
            )
            
        except SyntaxError as e:
            # Handle syntax errors
            syntax_issue = CodeIssue(
                issue_type=IssueType.SYNTAX_ERROR,
                severity=Severity.CRITICAL,
                message=f"Syntax error: {e.msg}",
                line_number=e.lineno or 1,
                column=e.offset or 0,
                file_path=file_path,
                suggestion="Fix the syntax error before proceeding"
            )
            
            return CodeAnalysisResult(
                file_path=file_path,
                language=CodeLanguage.PYTHON,
                metrics=self._get_default_metrics(code),
                issues=[syntax_issue],
                functions=[],
                classes=[],
                imports=[],
                dependencies=[],
                analysis_time=0.0,
                metadata={"syntax_error": True}
            )
    
    def _extract_python_functions(self, tree: ast.AST, code: str) -> List[FunctionInfo]:
        """Extract function information from Python AST."""
        functions = []
        lines = code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Extract parameters
                params = [arg.arg for arg in node.args.args]
                
                # Extract decorators
                decorators = [ast.unparse(dec) for dec in node.decorator_list]
                
                # Get docstring
                docstring = ast.get_docstring(node)
                
                # Calculate complexity (simplified)
                complexity = self._calculate_function_complexity(node)
                
                function_info = FunctionInfo(
                    name=node.name,
                    line_start=node.lineno,
                    line_end=node.end_lineno or node.lineno,
                    parameters=params,
                    return_type=None,  # Would need type annotations
                    complexity=complexity,
                    docstring=docstring,
                    is_async=isinstance(node, ast.AsyncFunctionDef),
                    decorators=decorators
                )
                
                functions.append(function_info)
        
        return functions
    
    def _extract_python_classes(self, tree: ast.AST, code: str) -> List[ClassInfo]:
        """Extract class information from Python AST."""
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Extract methods
                methods = []
                for item in node.body:
                    if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        method_info = FunctionInfo(
                            name=item.name,
                            line_start=item.lineno,
                            line_end=item.end_lineno or item.lineno,
                            parameters=[arg.arg for arg in item.args.args],
                            return_type=None,
                            complexity=self._calculate_function_complexity(item),
                            docstring=ast.get_docstring(item),
                            is_async=isinstance(item, ast.AsyncFunctionDef)
                        )
                        methods.append(method_info)
                
                # Extract inheritance
                inheritance = [ast.unparse(base) for base in node.bases]
                
                # Get docstring
                docstring = ast.get_docstring(node)
                
                class_info = ClassInfo(
                    name=node.name,
                    line_start=node.lineno,
                    line_end=node.end_lineno or node.lineno,
                    methods=methods,
                    attributes=[],  # Would need more complex analysis
                    inheritance=inheritance,
                    docstring=docstring,
                    is_abstract=any('ABC' in ast.unparse(base) for base in node.bases)
                )
                
                classes.append(class_info)
        
        return classes
    
    def _extract_python_imports(self, tree: ast.AST) -> List[str]:
        """Extract import statements from Python AST."""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    if alias.name == "*":
                        imports.append(f"{module}.*")
                    else:
                        imports.append(f"{module}.{alias.name}")
        
        return imports
    
    def _calculate_python_metrics(self, code: str, tree: ast.AST) -> CodeMetrics:
        """Calculate code metrics for Python code."""
        lines = code.splitlines()
        loc = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        
        # Calculate cyclomatic complexity
        complexity = self._calculate_cyclomatic_complexity(tree)
        
        # Calculate maintainability index (simplified)
        maintainability = max(0, 171 - 5.2 * complexity - 0.23 * loc)
        
        return CodeMetrics(
            lines_of_code=loc,
            cyclomatic_complexity=complexity,
            maintainability_index=maintainability,
            test_coverage=0.0,  # Would need test analysis
            code_duplication=0.0,  # Would need duplicate detection
            technical_debt_ratio=0.0,  # Would need more analysis
            documentation_coverage=self._calculate_doc_coverage(tree),
            security_score=80.0  # Would need security analysis
        )
    
    def _calculate_function_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity for a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate overall cyclomatic complexity."""
        complexity = 1
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                complexity += 1
        
        return complexity
    
    def _calculate_doc_coverage(self, tree: ast.AST) -> float:
        """Calculate documentation coverage."""
        total_items = 0
        documented_items = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                total_items += 1
                if ast.get_docstring(node):
                    documented_items += 1
        
        return (documented_items / total_items * 100) if total_items > 0 else 0.0
    
    def _detect_python_issues(self, code: str, tree: ast.AST) -> List[CodeIssue]:
        """Detect issues in Python code."""
        issues = []
        
        # Pattern-based detection
        patterns = self.issue_patterns.get(CodeLanguage.PYTHON, {})
        for issue_type, pattern_list in patterns.items():
            for pattern_info in pattern_list:
                pattern = pattern_info["pattern"]
                matches = re.finditer(pattern, code, re.MULTILINE)
                
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    
                    issue = CodeIssue(
                        issue_type=issue_type,
                        severity=pattern_info["severity"],
                        message=pattern_info["message"],
                        line_number=line_num,
                        column=match.start() - code.rfind('\n', 0, match.start()),
                        suggestion=pattern_info["suggestion"],
                        code_snippet=match.group(0)
                    )
                    issues.append(issue)
        
        # AST-based detection
        issues.extend(self._detect_ast_issues(tree))
        
        return issues
    
    def _detect_ast_issues(self, tree: ast.AST) -> List[CodeIssue]:
        """Detect issues using AST analysis."""
        issues = []
        
        for node in ast.walk(tree):
            # Detect unused variables (simplified)
            if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                # This would need more sophisticated analysis
                pass
            
            # Detect long functions
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    length = node.end_lineno - node.lineno
                    if length > 50:  # Arbitrary threshold
                        issue = CodeIssue(
                            issue_type=IssueType.COMPLEXITY,
                            severity=Severity.MEDIUM,
                            message=f"Function '{node.name}' is too long ({length} lines)",
                            line_number=node.lineno,
                            suggestion="Consider breaking this function into smaller functions"
                        )
                        issues.append(issue)
        
        return issues
    
    def _extract_dependencies(self, imports: List[str]) -> List[str]:
        """Extract external dependencies from imports."""
        # This would analyze imports to identify external packages
        external_deps = []
        
        for imp in imports:
            # Simple heuristic: if it's not a standard library module
            if not imp.startswith(('os', 'sys', 'json', 'time', 'datetime', 're')):
                package = imp.split('.')[0]
                if package not in external_deps:
                    external_deps.append(package)
        
        return external_deps
    
    def _get_default_metrics(self, code: str) -> CodeMetrics:
        """Get default metrics for code with errors."""
        return CodeMetrics(
            lines_of_code=len(code.splitlines()),
            cyclomatic_complexity=1,
            maintainability_index=0.0,
            test_coverage=0.0,
            code_duplication=0.0,
            technical_debt_ratio=100.0,
            documentation_coverage=0.0,
            security_score=0.0
        )
    
    # Placeholder methods for other languages
    async def _analyze_javascript(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze JavaScript code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.JAVASCRIPT)
    
    async def _analyze_typescript(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze TypeScript code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.TYPESCRIPT)
    
    async def _analyze_java(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze Java code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.JAVA)
    
    async def _analyze_go(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze Go code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.GO)
    
    async def _analyze_rust(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze Rust code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.RUST)
    
    async def _analyze_cpp(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze C++ code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.CPP)
    
    async def _analyze_csharp(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze C# code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.CSHARP)
    
    async def _analyze_php(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze PHP code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.PHP)
    
    async def _analyze_ruby(self, code: str, file_path: str) -> CodeAnalysisResult:
        """Analyze Ruby code."""
        return await self._analyze_generic(code, file_path, CodeLanguage.RUBY)
    
    async def _analyze_generic(self, code: str, file_path: str, language: CodeLanguage = CodeLanguage.UNKNOWN) -> CodeAnalysisResult:
        """Generic analysis for unsupported languages."""
        lines = code.splitlines()
        loc = len([line for line in lines if line.strip()])
        
        # Basic pattern-based analysis
        issues = []
        patterns = self.issue_patterns.get(language, {})
        
        for issue_type, pattern_list in patterns.items():
            for pattern_info in pattern_list:
                pattern = pattern_info["pattern"]
                matches = re.finditer(pattern, code, re.MULTILINE)
                
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    
                    issue = CodeIssue(
                        issue_type=issue_type,
                        severity=pattern_info["severity"],
                        message=pattern_info["message"],
                        line_number=line_num,
                        suggestion=pattern_info["suggestion"]
                    )
                    issues.append(issue)
        
        return CodeAnalysisResult(
            file_path=file_path,
            language=language,
            metrics=CodeMetrics(
                lines_of_code=loc,
                cyclomatic_complexity=1,
                maintainability_index=70.0,
                test_coverage=0.0,
                code_duplication=0.0,
                technical_debt_ratio=0.0,
                documentation_coverage=0.0,
                security_score=70.0
            ),
            issues=issues,
            functions=[],
            classes=[],
            imports=[],
            dependencies=[],
            analysis_time=0.0
        )
