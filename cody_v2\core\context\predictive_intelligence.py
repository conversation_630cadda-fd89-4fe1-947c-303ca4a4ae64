#!/usr/bin/env python3

"""
Predictive Intelligence for CODY v2.0
Advanced prediction and prefetching for intelligent assistance
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import threading

from .context_manager import ContextManager, ContextType, ContextScope

logger = logging.getLogger('CODY.PredictiveIntelligence')

class PredictionType(Enum):
    """Types of predictions."""
    NEXT_COMMAND = "next_command"
    FILE_NEEDED = "file_needed"
    DEPENDENCY_REQUIRED = "dependency_required"
    ERROR_LIKELY = "error_likely"
    OPTIMIZATION_OPPORTUNITY = "optimization_opportunity"
    WORKFLOW_STEP = "workflow_step"
    USER_INTENT = "user_intent"

class Confidence(Enum):
    """Confidence levels for predictions."""
    LOW = 0.3
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9

@dataclass
class Prediction:
    """Represents a prediction."""
    prediction_type: PredictionType
    description: str
    confidence: float
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    expires_at: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PredictionPattern:
    """Represents a learned pattern for predictions."""
    pattern_id: str
    trigger_conditions: List[str]
    predicted_actions: List[str]
    confidence: float
    usage_count: int = 0
    success_rate: float = 0.0
    last_used: float = field(default_factory=time.time)

class PredictiveIntelligence:
    """
    Advanced predictive intelligence system for CODY.
    
    Features:
    - Pattern learning from user behavior
    - Predictive prefetching of resources
    - Intelligent workflow suggestions
    - Error prediction and prevention
    - Context-aware recommendations
    - Adaptive learning from feedback
    """
    
    def __init__(self, context_manager: ContextManager):
        """Initialize predictive intelligence."""
        self.context_manager = context_manager
        
        # Pattern storage
        self.patterns: Dict[str, PredictionPattern] = {}
        self.user_behavior_history: deque = deque(maxlen=1000)
        self.prediction_cache: Dict[str, List[Prediction]] = {}
        
        # Learning parameters
        self.min_pattern_occurrences = 3
        self.pattern_confidence_threshold = 0.5
        self.cache_ttl = 300.0  # 5 minutes
        
        # Prediction engines
        self.prediction_engines = self._initialize_prediction_engines()
        
        # Threading
        self.lock = threading.RLock()
        self.background_thread = None
        self.shutdown_event = threading.Event()
        
        # Start background processing
        self._start_background_processing()
        
        logger.info("Predictive intelligence initialized")
    
    def _initialize_prediction_engines(self) -> Dict[PredictionType, Callable]:
        """Initialize prediction engines for different types."""
        return {
            PredictionType.NEXT_COMMAND: self._predict_next_command,
            PredictionType.FILE_NEEDED: self._predict_file_needed,
            PredictionType.DEPENDENCY_REQUIRED: self._predict_dependency_required,
            PredictionType.ERROR_LIKELY: self._predict_error_likely,
            PredictionType.OPTIMIZATION_OPPORTUNITY: self._predict_optimization,
            PredictionType.WORKFLOW_STEP: self._predict_workflow_step,
            PredictionType.USER_INTENT: self._predict_user_intent
        }
    
    def _start_background_processing(self):
        """Start background processing thread."""
        self.background_thread = threading.Thread(
            target=self._background_loop,
            daemon=True,
            name="PredictiveIntelligence"
        )
        self.background_thread.start()
    
    def _background_loop(self):
        """Background processing loop."""
        while not self.shutdown_event.is_set():
            try:
                # Update patterns
                self._update_patterns()
                
                # Clean cache
                self._clean_prediction_cache()
                
                # Generate background predictions
                asyncio.run(self._generate_background_predictions())
                
                # Sleep
                self.shutdown_event.wait(60.0)  # Run every minute
                
            except Exception as e:
                logger.error(f"Error in predictive intelligence background loop: {e}")
    
    async def record_user_action(self, action: str, context: Dict[str, Any]):
        """
        Record user action for learning.
        
        Args:
            action: Action performed by user
            context: Context when action was performed
        """
        with self.lock:
            action_record = {
                "action": action,
                "context": context,
                "timestamp": time.time()
            }
            
            self.user_behavior_history.append(action_record)
            
            # Trigger pattern learning
            await self._learn_from_action(action_record)
    
    async def get_predictions(self, 
                            prediction_types: Optional[List[PredictionType]] = None,
                            min_confidence: float = 0.3) -> List[Prediction]:
        """
        Get current predictions.
        
        Args:
            prediction_types: Types of predictions to get
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of predictions
        """
        if prediction_types is None:
            prediction_types = list(PredictionType)
        
        all_predictions = []
        
        for prediction_type in prediction_types:
            predictions = await self._get_predictions_for_type(prediction_type)
            
            # Filter by confidence
            filtered_predictions = [
                p for p in predictions 
                if p.confidence >= min_confidence
            ]
            
            all_predictions.extend(filtered_predictions)
        
        # Sort by confidence
        all_predictions.sort(key=lambda x: x.confidence, reverse=True)
        
        return all_predictions
    
    async def _get_predictions_for_type(self, prediction_type: PredictionType) -> List[Prediction]:
        """Get predictions for a specific type."""
        # Check cache first
        cache_key = f"{prediction_type.value}_{int(time.time() / 60)}"  # Cache per minute
        
        if cache_key in self.prediction_cache:
            return self.prediction_cache[cache_key]
        
        # Generate predictions
        engine = self.prediction_engines.get(prediction_type)
        if engine:
            predictions = await engine()
        else:
            predictions = []
        
        # Cache results
        self.prediction_cache[cache_key] = predictions
        
        return predictions
    
    async def _predict_next_command(self) -> List[Prediction]:
        """Predict next likely commands."""
        predictions = []
        
        # Get recent context
        recent_contexts = self.context_manager.get_contexts_by_type(
            ContextType.TERMINAL_STATE, limit=5
        )
        
        if not recent_contexts:
            return predictions
        
        # Analyze command patterns
        recent_commands = []
        for context in recent_contexts:
            if "last_command" in context.data:
                recent_commands.append(context.data["last_command"])
        
        # Find patterns
        command_patterns = self._find_command_patterns(recent_commands)
        
        for pattern, confidence in command_patterns:
            prediction = Prediction(
                prediction_type=PredictionType.NEXT_COMMAND,
                description=f"Likely next command: {pattern}",
                confidence=confidence,
                data={"command": pattern, "reasoning": "Based on recent command history"},
                expires_at=time.time() + 300  # 5 minutes
            )
            predictions.append(prediction)
        
        return predictions
    
    async def _predict_file_needed(self) -> List[Prediction]:
        """Predict files that might be needed."""
        predictions = []
        
        # Get project context
        project_contexts = self.context_manager.get_contexts_by_type(
            ContextType.PROJECT_STATE, limit=3
        )
        
        for context in project_contexts:
            if "current_files" in context.data:
                current_files = context.data["current_files"]
                
                # Predict related files
                related_files = self._predict_related_files(current_files)
                
                for file_path, confidence in related_files:
                    prediction = Prediction(
                        prediction_type=PredictionType.FILE_NEEDED,
                        description=f"May need file: {file_path}",
                        confidence=confidence,
                        data={"file_path": file_path, "reason": "Related to current work"},
                        expires_at=time.time() + 600  # 10 minutes
                    )
                    predictions.append(prediction)
        
        return predictions
    
    async def _predict_dependency_required(self) -> List[Prediction]:
        """Predict dependencies that might be required."""
        predictions = []
        
        # Get file contexts
        file_contexts = self.context_manager.get_contexts_by_type(
            ContextType.FILE_CONTEXT, limit=5
        )
        
        for context in file_contexts:
            if "imports" in context.data:
                imports = context.data["imports"]
                missing_deps = self._analyze_missing_dependencies(imports)
                
                for dep, confidence in missing_deps:
                    prediction = Prediction(
                        prediction_type=PredictionType.DEPENDENCY_REQUIRED,
                        description=f"May need to install: {dep}",
                        confidence=confidence,
                        data={"dependency": dep, "install_command": f"pip install {dep}"},
                        expires_at=time.time() + 1800  # 30 minutes
                    )
                    predictions.append(prediction)
        
        return predictions
    
    async def _predict_error_likely(self) -> List[Prediction]:
        """Predict likely errors."""
        predictions = []
        
        # Analyze recent code changes
        file_contexts = self.context_manager.get_contexts_by_type(
            ContextType.FILE_CONTEXT, limit=3
        )
        
        for context in file_contexts:
            if "recent_changes" in context.data:
                changes = context.data["recent_changes"]
                error_risks = self._analyze_error_risks(changes)
                
                for risk, confidence in error_risks:
                    prediction = Prediction(
                        prediction_type=PredictionType.ERROR_LIKELY,
                        description=f"Potential error: {risk}",
                        confidence=confidence,
                        data={"error_type": risk, "prevention": "Run tests before proceeding"},
                        expires_at=time.time() + 900  # 15 minutes
                    )
                    predictions.append(prediction)
        
        return predictions
    
    async def _predict_optimization(self) -> List[Prediction]:
        """Predict optimization opportunities."""
        predictions = []
        
        # This would analyze code patterns for optimization opportunities
        # For now, return basic suggestions
        
        prediction = Prediction(
            prediction_type=PredictionType.OPTIMIZATION_OPPORTUNITY,
            description="Consider running code analysis for optimization opportunities",
            confidence=0.4,
            data={"suggestion": "Use code analyzer to find performance improvements"},
            expires_at=time.time() + 3600  # 1 hour
        )
        predictions.append(prediction)
        
        return predictions
    
    async def _predict_workflow_step(self) -> List[Prediction]:
        """Predict next workflow steps."""
        predictions = []
        
        # Get workflow context
        workflow_contexts = self.context_manager.get_contexts_by_type(
            ContextType.WORKFLOW, limit=3
        )
        
        for context in workflow_contexts:
            if "current_step" in context.data:
                current_step = context.data["current_step"]
                next_steps = self._predict_next_workflow_steps(current_step)
                
                for step, confidence in next_steps:
                    prediction = Prediction(
                        prediction_type=PredictionType.WORKFLOW_STEP,
                        description=f"Next step: {step}",
                        confidence=confidence,
                        data={"step": step, "workflow": context.data.get("workflow_name", "unknown")},
                        expires_at=time.time() + 1200  # 20 minutes
                    )
                    predictions.append(prediction)
        
        return predictions
    
    async def _predict_user_intent(self) -> List[Prediction]:
        """Predict user intent based on context."""
        predictions = []
        
        # Get recent user intents
        intent_contexts = self.context_manager.get_contexts_by_type(
            ContextType.USER_INTENT, limit=5
        )
        
        if len(intent_contexts) >= 2:
            # Analyze intent patterns
            intent_patterns = self._analyze_intent_patterns(intent_contexts)
            
            for intent, confidence in intent_patterns:
                prediction = Prediction(
                    prediction_type=PredictionType.USER_INTENT,
                    description=f"User likely wants to: {intent}",
                    confidence=confidence,
                    data={"intent": intent, "reasoning": "Based on recent activity patterns"},
                    expires_at=time.time() + 600  # 10 minutes
                )
                predictions.append(prediction)
        
        return predictions
    
    def _find_command_patterns(self, commands: List[str]) -> List[Tuple[str, float]]:
        """Find patterns in command history."""
        if len(commands) < 2:
            return []
        
        patterns = []
        
        # Simple pattern: if last command was X, next is often Y
        command_pairs = defaultdict(int)
        for i in range(len(commands) - 1):
            pair = (commands[i], commands[i + 1])
            command_pairs[pair] += 1
        
        # Find most common patterns
        if commands:
            last_command = commands[-1]
            for (cmd1, cmd2), count in command_pairs.items():
                if cmd1 == last_command and count >= 2:
                    confidence = min(0.9, count / len(commands))
                    patterns.append((cmd2, confidence))
        
        return patterns[:3]  # Top 3 patterns
    
    def _predict_related_files(self, current_files: List[str]) -> List[Tuple[str, float]]:
        """Predict related files based on current files."""
        related = []
        
        for file_path in current_files:
            # Predict test files
            if not file_path.endswith("_test.py") and not file_path.endswith(".test.js"):
                if file_path.endswith(".py"):
                    test_file = file_path.replace(".py", "_test.py")
                    related.append((test_file, 0.7))
                elif file_path.endswith(".js"):
                    test_file = file_path.replace(".js", ".test.js")
                    related.append((test_file, 0.7))
            
            # Predict config files
            if "src/" in file_path:
                config_files = ["package.json", "requirements.txt", "Cargo.toml"]
                for config in config_files:
                    related.append((config, 0.5))
        
        return related[:5]  # Top 5 predictions
    
    def _analyze_missing_dependencies(self, imports: List[str]) -> List[Tuple[str, float]]:
        """Analyze imports for missing dependencies."""
        missing = []
        
        # Common import to package mappings
        import_mappings = {
            "requests": "requests",
            "numpy": "numpy",
            "pandas": "pandas",
            "flask": "flask",
            "django": "django",
            "fastapi": "fastapi",
            "aiohttp": "aiohttp"
        }
        
        for imp in imports:
            package = imp.split('.')[0]
            if package in import_mappings:
                missing.append((import_mappings[package], 0.6))
        
        return missing[:3]  # Top 3 predictions
    
    def _analyze_error_risks(self, changes: List[str]) -> List[Tuple[str, float]]:
        """Analyze code changes for error risks."""
        risks = []
        
        for change in changes:
            if "import" in change:
                risks.append(("Import error - module not found", 0.4))
            if "def " in change:
                risks.append(("Function signature mismatch", 0.3))
            if "=" in change and "==" not in change:
                risks.append(("Variable assignment error", 0.2))
        
        return risks[:3]  # Top 3 risks
    
    def _predict_next_workflow_steps(self, current_step: str) -> List[Tuple[str, float]]:
        """Predict next workflow steps."""
        workflow_patterns = {
            "create_project": [("setup_dependencies", 0.8), ("create_files", 0.7)],
            "setup_dependencies": [("create_files", 0.9), ("write_code", 0.6)],
            "create_files": [("write_code", 0.8), ("add_tests", 0.6)],
            "write_code": [("add_tests", 0.7), ("debug", 0.5)],
            "add_tests": [("run_tests", 0.9), ("debug", 0.4)],
            "run_tests": [("debug", 0.6), ("deploy", 0.3)],
            "debug": [("run_tests", 0.7), ("write_code", 0.5)]
        }
        
        return workflow_patterns.get(current_step, [])
    
    def _analyze_intent_patterns(self, intent_contexts: List) -> List[Tuple[str, float]]:
        """Analyze patterns in user intents."""
        intents = []
        
        for context in intent_contexts:
            if "intent" in context.data:
                intents.append(context.data["intent"])
        
        # Simple pattern analysis
        if len(intents) >= 2:
            last_intent = intents[-1]
            
            # Common intent transitions
            transitions = {
                "create_project": [("write_code", 0.8), ("setup_environment", 0.6)],
                "write_code": [("debug", 0.7), ("test", 0.6)],
                "debug": [("test", 0.8), ("write_code", 0.5)],
                "test": [("deploy", 0.6), ("debug", 0.4)]
            }
            
            return transitions.get(last_intent, [])
        
        return []
    
    async def _learn_from_action(self, action_record: Dict[str, Any]):
        """Learn patterns from user actions."""
        # This would implement more sophisticated pattern learning
        # For now, just log the action
        logger.debug(f"Learning from action: {action_record['action']}")
    
    def _update_patterns(self):
        """Update learned patterns based on recent behavior."""
        # This would analyze behavior history and update patterns
        pass
    
    def _clean_prediction_cache(self):
        """Clean expired predictions from cache."""
        current_time = time.time()
        expired_keys = []
        
        for key, predictions in self.prediction_cache.items():
            # Remove expired predictions
            valid_predictions = [
                p for p in predictions
                if p.expires_at is None or p.expires_at > current_time
            ]
            
            if not valid_predictions:
                expired_keys.append(key)
            else:
                self.prediction_cache[key] = valid_predictions
        
        # Remove empty cache entries
        for key in expired_keys:
            del self.prediction_cache[key]
    
    async def _generate_background_predictions(self):
        """Generate predictions in background."""
        try:
            # Generate predictions for all types
            await self.get_predictions()
        except Exception as e:
            logger.error(f"Error generating background predictions: {e}")
    
    async def provide_feedback(self, prediction_id: str, was_helpful: bool):
        """Provide feedback on prediction accuracy."""
        # This would update pattern confidence based on feedback
        logger.info(f"Feedback for prediction {prediction_id}: {'helpful' if was_helpful else 'not helpful'}")
    
    def get_prediction_stats(self) -> Dict[str, Any]:
        """Get prediction statistics."""
        total_predictions = sum(len(preds) for preds in self.prediction_cache.values())
        
        return {
            "total_cached_predictions": total_predictions,
            "cache_entries": len(self.prediction_cache),
            "learned_patterns": len(self.patterns),
            "behavior_history_size": len(self.user_behavior_history),
            "prediction_types": len(self.prediction_engines)
        }
    
    def shutdown(self):
        """Shutdown predictive intelligence."""
        logger.info("Shutting down predictive intelligence")
        self.shutdown_event.set()
        
        if self.background_thread:
            self.background_thread.join(timeout=5.0)
        
        logger.info("Predictive intelligence shutdown complete")
