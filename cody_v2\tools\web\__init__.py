#!/usr/bin/env python3

"""
Web Integration Tools for CODY v2.0
Advanced web search, URL fetching, and RAG-based information retrieval
"""

from .web_search import (
    WebSearchEngine,
    SearchSource,
    ContentType as SearchContentType,
    SearchResult,
    SearchQuery
)

from .url_fetcher import (
    URLFetcher,
    ContentType as FetchContentType,
    FetchedContent
)

from .rag_system import (
    RAGSystem,
    DocumentType,
    KnowledgeChunk,
    RAGQuery,
    RAGResponse
)

__all__ = [
    # Web Search
    'WebSearchEngine',
    'SearchSource',
    'SearchContentType',
    'SearchResult',
    'SearchQuery',
    
    # URL Fetching
    'URLFetcher',
    'FetchContentType',
    'FetchedContent',
    
    # RAG System
    'RAGSystem',
    'DocumentType',
    'KnowledgeChunk',
    'RAGQuery',
    'RAGResponse'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Web integration tools with search, fetching, and RAG capabilities"
