#!/usr/bin/env python3

"""
Entity Extractor for CODY v2.0
Advanced entity extraction and classification
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger('CODY.EntityExtractor')

class EntityType(Enum):
    """Types of entities that can be extracted."""
    FILE_PATH = "file_path"
    URL = "url"
    EMAIL = "email"
    PHONE = "phone"
    DATE = "date"
    TIME = "time"
    NUMBER = "number"
    VERSION = "version"
    IP_ADDRESS = "ip_address"
    PORT = "port"
    FUNCTION_NAME = "function_name"
    CLASS_NAME = "class_name"
    VARIABLE_NAME = "variable_name"
    PACKAGE_NAME = "package_name"
    COMMAND = "command"
    FRAMEWORK = "framework"
    DATABASE = "database"
    PROGRAMMING_LANGUAGE = "programming_language"
    TECHNOLOGY = "technology"
    CREDENTIAL = "credential"
    ENVIRONMENT = "environment"

@dataclass
class ExtractedEntity:
    """Represents an extracted entity with metadata."""
    type: EntityType
    value: str
    confidence: float
    start_pos: int
    end_pos: int
    context: str
    metadata: Dict[str, Any] = field(default_factory=dict)

class EntityExtractor:
    """
    Advanced entity extractor for technical content.
    Specialized for programming and development contexts.
    """
    
    def __init__(self):
        """Initialize the entity extractor."""
        self.patterns = self._initialize_patterns()
        self.context_enhancers = self._initialize_context_enhancers()
        self.validation_rules = self._initialize_validation_rules()
        
        logger.info("Entity extractor initialized")
    
    def _initialize_patterns(self) -> Dict[EntityType, List[Dict[str, Any]]]:
        """Initialize extraction patterns for different entity types."""
        return {
            EntityType.FILE_PATH: [
                {
                    "pattern": r'(?:[a-zA-Z]:\\|/)?(?:[^/\\:*?"<>|\r\n\s]+[/\\])*[^/\\:*?"<>|\r\n\s]*\.[a-zA-Z0-9]+',
                    "confidence": 0.9,
                    "context_required": False
                },
                {
                    "pattern": r'(?:\.\/|\.\.\/|\/)[^/\s]+(?:\/[^/\s]+)*\/?',
                    "confidence": 0.8,
                    "context_required": False
                }
            ],
            EntityType.URL: [
                {
                    "pattern": r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?',
                    "confidence": 0.95,
                    "context_required": False
                },
                {
                    "pattern": r'www\.(?:[-\w.])+\.(?:[a-zA-Z]{2,})(?:/(?:[\w/_.])*)?',
                    "confidence": 0.85,
                    "context_required": False
                }
            ],
            EntityType.EMAIL: [
                {
                    "pattern": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                    "confidence": 0.95,
                    "context_required": False
                }
            ],
            EntityType.VERSION: [
                {
                    "pattern": r'\b(?:v|version\s+)?(\d+\.\d+(?:\.\d+)?(?:-[a-zA-Z0-9]+)?)\b',
                    "confidence": 0.8,
                    "context_required": True
                }
            ],
            EntityType.IP_ADDRESS: [
                {
                    "pattern": r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
                    "confidence": 0.9,
                    "context_required": False
                }
            ],
            EntityType.PORT: [
                {
                    "pattern": r'\b(?:port\s+)?(\d{1,5})\b',
                    "confidence": 0.7,
                    "context_required": True
                }
            ],
            EntityType.FUNCTION_NAME: [
                {
                    "pattern": r'\b(?:function|def|func|fn)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                    "confidence": 0.95,
                    "context_required": False
                },
                {
                    "pattern": r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',
                    "confidence": 0.7,
                    "context_required": True
                }
            ],
            EntityType.CLASS_NAME: [
                {
                    "pattern": r'\b(?:class|interface|struct)\s+([A-Z][a-zA-Z0-9_]*)',
                    "confidence": 0.95,
                    "context_required": False
                },
                {
                    "pattern": r'\b([A-Z][a-zA-Z0-9_]*)\s*\{',
                    "confidence": 0.8,
                    "context_required": True
                }
            ],
            EntityType.VARIABLE_NAME: [
                {
                    "pattern": r'\b(?:var|let|const|int|string|bool|float|double)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                    "confidence": 0.9,
                    "context_required": False
                }
            ],
            EntityType.PACKAGE_NAME: [
                {
                    "pattern": r'\b(?:import|from|require|use|include)\s+([a-zA-Z0-9._-]+)',
                    "confidence": 0.9,
                    "context_required": False
                },
                {
                    "pattern": r'\b(?:npm|pip|yarn|cargo|go)\s+(?:install|add)\s+([a-zA-Z0-9._-]+)',
                    "confidence": 0.95,
                    "context_required": False
                }
            ],
            EntityType.COMMAND: [
                {
                    "pattern": r'\b(npm|pip|yarn|cargo|go|docker|git|kubectl|mvn|gradle)\s+([a-zA-Z0-9\-_]+)',
                    "confidence": 0.9,
                    "context_required": False
                }
            ],
            EntityType.FRAMEWORK: [
                {
                    "pattern": r'\b(react|vue|angular|django|flask|express|spring|laravel|rails|next\.js|nuxt\.js|svelte|ember)\b',
                    "confidence": 0.85,
                    "context_required": False
                }
            ],
            EntityType.DATABASE: [
                {
                    "pattern": r'\b(mysql|postgresql|postgres|mongodb|redis|sqlite|oracle|cassandra|dynamodb|firestore|supabase)\b',
                    "confidence": 0.85,
                    "context_required": False
                }
            ],
            EntityType.PROGRAMMING_LANGUAGE: [
                {
                    "pattern": r'\b(python|javascript|typescript|java|go|rust|cpp|c\+\+|csharp|c#|php|ruby|swift|kotlin|dart|scala|r|matlab|shell|bash|powershell)\b',
                    "confidence": 0.9,
                    "context_required": False
                }
            ],
            EntityType.TECHNOLOGY: [
                {
                    "pattern": r'\b(aws|azure|gcp|kubernetes|docker|terraform|ansible|jenkins|github|gitlab|bitbucket|jira|confluence)\b',
                    "confidence": 0.8,
                    "context_required": False
                }
            ],
            EntityType.ENVIRONMENT: [
                {
                    "pattern": r'\b(development|dev|staging|stage|production|prod|test|testing|local|localhost)\b',
                    "confidence": 0.8,
                    "context_required": True
                }
            ]
        }
    
    def _initialize_context_enhancers(self) -> Dict[EntityType, List[str]]:
        """Initialize context keywords that enhance entity detection."""
        return {
            EntityType.VERSION: ["version", "release", "update", "upgrade", "v"],
            EntityType.PORT: ["port", "listen", "bind", "server", "service"],
            EntityType.FUNCTION_NAME: ["function", "method", "call", "invoke", "execute"],
            EntityType.CLASS_NAME: ["class", "object", "instance", "type"],
            EntityType.ENVIRONMENT: ["environment", "env", "deploy", "deployment", "server"]
        }
    
    def _initialize_validation_rules(self) -> Dict[EntityType, callable]:
        """Initialize validation rules for extracted entities."""
        return {
            EntityType.IP_ADDRESS: self._validate_ip_address,
            EntityType.PORT: self._validate_port,
            EntityType.EMAIL: self._validate_email,
            EntityType.URL: self._validate_url,
            EntityType.VERSION: self._validate_version
        }
    
    def extract_entities(self, text: str, context: Optional[Dict[str, Any]] = None) -> List[ExtractedEntity]:
        """
        Extract entities from text.
        
        Args:
            text: Input text to analyze
            context: Optional context information
            
        Returns:
            List of extracted entities
        """
        entities = []
        
        for entity_type, pattern_configs in self.patterns.items():
            for config in pattern_configs:
                pattern = config["pattern"]
                base_confidence = config["confidence"]
                context_required = config["context_required"]
                
                matches = re.finditer(pattern, text, re.IGNORECASE)
                
                for match in matches:
                    # Extract the entity value
                    if match.groups():
                        value = match.group(1)
                        start_pos = match.start(1)
                        end_pos = match.end(1)
                    else:
                        value = match.group(0)
                        start_pos = match.start()
                        end_pos = match.end()
                    
                    # Get context around the match
                    context_start = max(0, start_pos - 50)
                    context_end = min(len(text), end_pos + 50)
                    entity_context = text[context_start:context_end]
                    
                    # Calculate confidence
                    confidence = self._calculate_confidence(
                        entity_type, value, entity_context, base_confidence, context_required
                    )
                    
                    # Validate entity if validation rule exists
                    if entity_type in self.validation_rules:
                        if not self.validation_rules[entity_type](value):
                            continue
                    
                    # Create entity
                    entity = ExtractedEntity(
                        type=entity_type,
                        value=value,
                        confidence=confidence,
                        start_pos=start_pos,
                        end_pos=end_pos,
                        context=entity_context,
                        metadata={
                            "pattern_used": pattern,
                            "context_enhanced": context_required
                        }
                    )
                    
                    entities.append(entity)
        
        # Remove duplicates and overlapping entities
        entities = self._deduplicate_entities(entities)
        
        # Sort by confidence
        entities.sort(key=lambda x: x.confidence, reverse=True)
        
        return entities
    
    def _calculate_confidence(self, entity_type: EntityType, value: str, context: str, 
                            base_confidence: float, context_required: bool) -> float:
        """Calculate confidence score for an entity."""
        confidence = base_confidence
        
        # Boost confidence if context enhancers are present
        if entity_type in self.context_enhancers:
            enhancers = self.context_enhancers[entity_type]
            context_lower = context.lower()
            
            for enhancer in enhancers:
                if enhancer in context_lower:
                    confidence += 0.1
                    break
        
        # Reduce confidence if context is required but not strong
        if context_required:
            if not self._has_strong_context(entity_type, context):
                confidence -= 0.2
        
        # Boost confidence for well-formed entities
        if entity_type == EntityType.FILE_PATH and '.' in value:
            confidence += 0.1
        elif entity_type == EntityType.FUNCTION_NAME and value.islower():
            confidence += 0.05
        elif entity_type == EntityType.CLASS_NAME and value[0].isupper():
            confidence += 0.05
        
        return max(0.0, min(1.0, confidence))
    
    def _has_strong_context(self, entity_type: EntityType, context: str) -> bool:
        """Check if the context strongly supports the entity type."""
        context_lower = context.lower()
        
        if entity_type == EntityType.PORT:
            return any(word in context_lower for word in ["port", "listen", "bind", "server"])
        elif entity_type == EntityType.VERSION:
            return any(word in context_lower for word in ["version", "v", "release", "update"])
        elif entity_type == EntityType.ENVIRONMENT:
            return any(word in context_lower for word in ["environment", "deploy", "server"])
        
        return True
    
    def _deduplicate_entities(self, entities: List[ExtractedEntity]) -> List[ExtractedEntity]:
        """Remove duplicate and overlapping entities."""
        if not entities:
            return entities
        
        # Sort by start position
        entities.sort(key=lambda x: x.start_pos)
        
        deduplicated = []
        
        for entity in entities:
            # Check for overlaps with existing entities
            overlaps = False
            for existing in deduplicated:
                if (entity.start_pos < existing.end_pos and 
                    entity.end_pos > existing.start_pos):
                    # Overlapping entities - keep the one with higher confidence
                    if entity.confidence > existing.confidence:
                        deduplicated.remove(existing)
                        break
                    else:
                        overlaps = True
                        break
            
            if not overlaps:
                deduplicated.append(entity)
        
        return deduplicated
    
    # Validation methods
    def _validate_ip_address(self, value: str) -> bool:
        """Validate IP address format."""
        parts = value.split('.')
        if len(parts) != 4:
            return False
        
        try:
            for part in parts:
                num = int(part)
                if not 0 <= num <= 255:
                    return False
            return True
        except ValueError:
            return False
    
    def _validate_port(self, value: str) -> bool:
        """Validate port number."""
        try:
            port = int(value)
            return 1 <= port <= 65535
        except ValueError:
            return False
    
    def _validate_email(self, value: str) -> bool:
        """Validate email format."""
        return '@' in value and '.' in value.split('@')[1]
    
    def _validate_url(self, value: str) -> bool:
        """Validate URL format."""
        return value.startswith(('http://', 'https://')) or value.startswith('www.')
    
    def _validate_version(self, value: str) -> bool:
        """Validate version format."""
        parts = value.split('.')
        return len(parts) >= 2 and all(part.isdigit() for part in parts[:2])
    
    def get_entities_by_type(self, entities: List[ExtractedEntity], 
                           entity_type: EntityType) -> List[ExtractedEntity]:
        """Get entities of a specific type."""
        return [entity for entity in entities if entity.type == entity_type]
    
    def get_high_confidence_entities(self, entities: List[ExtractedEntity], 
                                   threshold: float = 0.8) -> List[ExtractedEntity]:
        """Get entities with confidence above threshold."""
        return [entity for entity in entities if entity.confidence >= threshold]
