#!/usr/bin/env python3

"""
Language Detector for CODY v2.0
Multi-language detection with support for English, Hindi, and mixed languages
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger('CODY.LanguageDetector')

class Language(Enum):
    """Supported languages."""
    ENGLISH = "en"
    HINDI = "hi"
    MIXED = "mixed"
    UNKNOWN = "unknown"

@dataclass
class LanguageDetectionResult:
    """Result of language detection."""
    primary_language: Language
    confidence: float
    language_scores: Dict[Language, float]
    mixed_segments: List[Tuple[str, Language]]
    metadata: Dict[str, any]

class LanguageDetector:
    """
    Advanced language detector for multi-language support.
    Specialized for English, Hindi, and mixed language content.
    """
    
    def __init__(self):
        """Initialize the language detector."""
        self.language_patterns = self._initialize_language_patterns()
        self.script_patterns = self._initialize_script_patterns()
        self.common_words = self._initialize_common_words()
        self.technical_terms = self._initialize_technical_terms()
        
        logger.info("Language detector initialized")
    
    def _initialize_language_patterns(self) -> Dict[Language, List[str]]:
        """Initialize language-specific patterns."""
        return {
            Language.HINDI: [
                # Hindi question words
                r'\b(kya|kaise|kahan|kab|kyun|kaun|kitna|kitne)\b',
                # Hindi verbs
                r'\b(karo|karna|hona|hai|hain|tha|the|thi|banao|bnao|dekho|samjhao|batao|sikhao)\b',
                # Hindi pronouns and particles
                r'\b(mujhe|tumhe|usse|isse|waha|yaha|isme|usme|aur|ya|lekin|par|phir|tab|jab|agar|to)\b',
                # Hindi helping words
                r'\b(chahiye|hoga|hogi|krna|krke|krte|wala|wali|wale)\b',
                # Hindi technical terms (Hinglish)
                r'\b(file|folder|code|debug|fix|help|search|create|delete|install|run)\b.*\b(karo|karna|hai|hona)\b'
            ],
            Language.ENGLISH: [
                # English question words
                r'\b(what|how|where|when|why|who|which|whose)\b',
                # English verbs
                r'\b(create|make|build|generate|write|develop|implement|debug|fix|solve|search|find|help)\b',
                # English articles and prepositions
                r'\b(the|a|an|in|on|at|by|for|with|from|to|of|and|or|but|if|then|this|that)\b',
                # English modal verbs
                r'\b(can|could|will|would|should|shall|may|might|must)\b',
                # Technical English
                r'\b(application|function|variable|database|server|client|framework|library|package)\b'
            ]
        }
    
    def _initialize_script_patterns(self) -> Dict[str, str]:
        """Initialize script detection patterns."""
        return {
            "devanagari": r'[\u0900-\u097F]+',  # Hindi/Devanagari script
            "latin": r'[a-zA-Z]+',              # Latin script (English)
            "numbers": r'[\d]+',                # Numbers
            "punctuation": r'[.,!?;:(){}[\]"\'`~@#$%^&*+=<>/-]'  # Punctuation
        }
    
    def _initialize_common_words(self) -> Dict[Language, List[str]]:
        """Initialize common words for each language."""
        return {
            Language.HINDI: [
                "hai", "hain", "tha", "the", "thi", "kya", "kaise", "kahan", "kab", "kyun",
                "mujhe", "tumhe", "usse", "isse", "aur", "ya", "lekin", "par", "phir",
                "karo", "karna", "hona", "banao", "dekho", "samjhao", "batao", "sikhao",
                "chahiye", "hoga", "hogi", "wala", "wali", "wale", "isme", "usme"
            ],
            Language.ENGLISH: [
                "the", "and", "for", "are", "but", "not", "you", "all", "can", "had",
                "her", "was", "one", "our", "out", "day", "get", "has", "him", "his",
                "how", "its", "may", "new", "now", "old", "see", "two", "way", "who",
                "what", "when", "where", "why", "will", "with", "would", "this", "that"
            ]
        }
    
    def _initialize_technical_terms(self) -> List[str]:
        """Initialize technical terms that are language-neutral."""
        return [
            "api", "app", "code", "debug", "file", "folder", "git", "html", "css", "js",
            "json", "sql", "url", "http", "https", "npm", "pip", "docker", "kubernetes",
            "react", "vue", "angular", "python", "javascript", "java", "go", "rust"
        ]
    
    def detect_language(self, text: str) -> LanguageDetectionResult:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Language detection result with confidence scores
        """
        if not text.strip():
            return LanguageDetectionResult(
                primary_language=Language.UNKNOWN,
                confidence=0.0,
                language_scores={},
                mixed_segments=[],
                metadata={"reason": "empty_text"}
            )
        
        # Calculate language scores
        language_scores = self._calculate_language_scores(text)
        
        # Detect mixed segments
        mixed_segments = self._detect_mixed_segments(text)
        
        # Determine primary language
        primary_language, confidence = self._determine_primary_language(
            language_scores, mixed_segments, text
        )
        
        return LanguageDetectionResult(
            primary_language=primary_language,
            confidence=confidence,
            language_scores=language_scores,
            mixed_segments=mixed_segments,
            metadata={
                "text_length": len(text),
                "word_count": len(text.split()),
                "has_devanagari": bool(re.search(self.script_patterns["devanagari"], text)),
                "technical_terms_count": self._count_technical_terms(text)
            }
        )
    
    def _calculate_language_scores(self, text: str) -> Dict[Language, float]:
        """Calculate scores for each language."""
        scores = {Language.HINDI: 0.0, Language.ENGLISH: 0.0}
        text_lower = text.lower()
        
        # Pattern-based scoring
        for language, patterns in self.language_patterns.items():
            if language in scores:
                for pattern in patterns:
                    matches = len(re.findall(pattern, text_lower))
                    scores[language] += matches * 0.5
        
        # Common words scoring
        words = text_lower.split()
        for language, common_words in self.common_words.items():
            if language in scores:
                for word in words:
                    if word in common_words:
                        scores[language] += 1.0
        
        # Script-based scoring
        devanagari_chars = len(re.findall(self.script_patterns["devanagari"], text))
        latin_chars = len(re.findall(self.script_patterns["latin"], text))
        
        if devanagari_chars > 0:
            scores[Language.HINDI] += devanagari_chars * 0.1
        
        if latin_chars > 0:
            scores[Language.ENGLISH] += latin_chars * 0.05
        
        # Normalize scores
        total_score = sum(scores.values())
        if total_score > 0:
            for language in scores:
                scores[language] = scores[language] / total_score
        
        return scores
    
    def _detect_mixed_segments(self, text: str) -> List[Tuple[str, Language]]:
        """Detect segments of different languages in mixed text."""
        segments = []
        
        # Split text into sentences
        sentences = re.split(r'[.!?]+', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # Analyze each sentence
            sentence_scores = self._calculate_language_scores(sentence)
            
            if sentence_scores[Language.HINDI] > sentence_scores[Language.ENGLISH]:
                if sentence_scores[Language.HINDI] > 0.6:
                    segments.append((sentence, Language.HINDI))
                else:
                    segments.append((sentence, Language.MIXED))
            elif sentence_scores[Language.ENGLISH] > sentence_scores[Language.HINDI]:
                if sentence_scores[Language.ENGLISH] > 0.6:
                    segments.append((sentence, Language.ENGLISH))
                else:
                    segments.append((sentence, Language.MIXED))
            else:
                segments.append((sentence, Language.MIXED))
        
        return segments
    
    def _determine_primary_language(self, language_scores: Dict[Language, float],
                                  mixed_segments: List[Tuple[str, Language]],
                                  text: str) -> Tuple[Language, float]:
        """Determine the primary language and confidence."""
        
        # Check if text is predominantly one language
        hindi_score = language_scores.get(Language.HINDI, 0.0)
        english_score = language_scores.get(Language.ENGLISH, 0.0)
        
        # High confidence thresholds
        if hindi_score > 0.8:
            return Language.HINDI, hindi_score
        elif english_score > 0.8:
            return Language.ENGLISH, english_score
        
        # Check for mixed language indicators
        has_devanagari = bool(re.search(self.script_patterns["devanagari"], text))
        has_latin = bool(re.search(self.script_patterns["latin"], text))
        
        if has_devanagari and has_latin:
            # Definitely mixed
            confidence = 0.9
            return Language.MIXED, confidence
        
        # Moderate confidence based on scores
        if hindi_score > english_score:
            if hindi_score > 0.6:
                return Language.HINDI, hindi_score
            else:
                return Language.MIXED, 0.7
        elif english_score > hindi_score:
            if english_score > 0.6:
                return Language.ENGLISH, english_score
            else:
                return Language.MIXED, 0.7
        else:
            # Scores are close, check other indicators
            technical_terms = self._count_technical_terms(text)
            if technical_terms > len(text.split()) * 0.3:
                # Lots of technical terms, likely English or mixed
                return Language.MIXED, 0.6
            else:
                return Language.UNKNOWN, 0.3
    
    def _count_technical_terms(self, text: str) -> int:
        """Count technical terms in the text."""
        text_lower = text.lower()
        count = 0
        
        for term in self.technical_terms:
            if term in text_lower:
                count += text_lower.count(term)
        
        return count
    
    def is_mixed_language(self, text: str, threshold: float = 0.3) -> bool:
        """Check if text contains mixed languages."""
        result = self.detect_language(text)
        
        # Check if both languages have significant presence
        hindi_score = result.language_scores.get(Language.HINDI, 0.0)
        english_score = result.language_scores.get(Language.ENGLISH, 0.0)
        
        return (hindi_score > threshold and english_score > threshold) or \
               result.primary_language == Language.MIXED
    
    def get_dominant_language(self, text: str) -> Language:
        """Get the dominant language in the text."""
        result = self.detect_language(text)
        
        if result.primary_language == Language.MIXED:
            # For mixed text, return the language with higher score
            hindi_score = result.language_scores.get(Language.HINDI, 0.0)
            english_score = result.language_scores.get(Language.ENGLISH, 0.0)
            
            if hindi_score > english_score:
                return Language.HINDI
            else:
                return Language.ENGLISH
        
        return result.primary_language
    
    def extract_language_segments(self, text: str) -> Dict[Language, List[str]]:
        """Extract segments by language."""
        result = self.detect_language(text)
        segments = {Language.HINDI: [], Language.ENGLISH: [], Language.MIXED: []}
        
        for segment, language in result.mixed_segments:
            if language in segments:
                segments[language].append(segment)
        
        return segments
    
    def suggest_response_language(self, text: str) -> Language:
        """Suggest the best language for response based on input."""
        result = self.detect_language(text)
        
        # If input is clearly in one language, respond in the same
        if result.confidence > 0.8 and result.primary_language != Language.MIXED:
            return result.primary_language
        
        # For mixed or unclear input, prefer English for technical content
        technical_ratio = self._count_technical_terms(text) / max(1, len(text.split()))
        
        if technical_ratio > 0.2:
            return Language.ENGLISH
        
        # Default to the dominant language
        return self.get_dominant_language(text)
