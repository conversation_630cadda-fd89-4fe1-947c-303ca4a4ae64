# CODY v2.0 - Advanced SaaS AI Terminal Agent
# Core Dependencies

# Async HTTP client for API requests
aiohttp>=3.9.0
aiofiles>=23.2.0

# AI Model Providers
openai>=1.12.0                    # For DeepSeek API compatibility
google-generativeai>=0.3.0        # For Gemini models

# Web scraping and search
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0                  # For advanced web automation

# Natural Language Processing
spacy>=3.7.0
nltk>=3.8.1
transformers>=4.35.0              # For local NLP models

# Code Analysis and AST
ast-tools>=0.1.0
pylint>=3.0.0
black>=23.0.0                     # Code formatting
isort>=5.12.0                     # Import sorting

# Terminal and CLI
rich>=13.7.0                      # Rich terminal output
click>=8.1.0                      # CLI framework
prompt-toolkit>=3.0.0             # Advanced input handling
colorama>=0.4.6                   # Cross-platform colored output

# File System and Project Management
watchdog>=3.0.0                   # File system monitoring
gitpython>=3.1.40                 # Git integration
pathspec>=0.11.0                  # Path pattern matching

# Data Processing and Storage
pandas>=2.1.0                     # Data manipulation
numpy>=1.24.0                     # Numerical computing
sqlite3                           # Built-in database (Python standard library)
redis>=5.0.0                      # Optional: For advanced caching

# Configuration and Environment
python-dotenv>=1.0.0              # Environment variable management
pydantic>=2.5.0                   # Data validation
toml>=0.10.2                      # TOML configuration files

# Logging and Monitoring
structlog>=23.2.0                 # Structured logging
psutil>=5.9.0                     # System monitoring

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# Development Tools
mypy>=1.7.0                       # Type checking
flake8>=6.1.0                     # Linting
pre-commit>=3.5.0                 # Git hooks

# Optional: Advanced Features
docker>=6.1.0                     # Docker integration
kubernetes>=28.1.0                # Kubernetes integration
tensorflow>=2.15.0                # For local AI models (optional)
torch>=2.1.0                      # PyTorch for local models (optional)

# Security
cryptography>=41.0.0              # Encryption and security
keyring>=24.3.0                   # Secure credential storage

# Performance and Optimization
uvloop>=0.19.0                    # Fast event loop (Unix only)
orjson>=3.9.0                     # Fast JSON parsing
cachetools>=5.3.0                 # Caching utilities

# Documentation
sphinx>=7.2.0                     # Documentation generation
sphinx-rtd-theme>=1.3.0           # Read the Docs theme

# Deployment
gunicorn>=21.2.0                  # WSGI server
uvicorn>=0.24.0                   # ASGI server
