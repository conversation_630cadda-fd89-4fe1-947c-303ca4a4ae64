#!/usr/bin/env python3

"""
Refactoring Engine for CODY v2.0
Intelligent code refactoring with pattern recognition and automated improvements
"""

import ast
import logging
import re
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .code_analyzer import <PERSON>Analy<PERSON>, CodeLanguage, IssueType, CodeIssue

logger = logging.getLogger('CODY.RefactoringEngine')

class RefactoringType(Enum):
    """Types of refactoring operations."""
    EXTRACT_METHOD = "extract_method"
    EXTRACT_VARIABLE = "extract_variable"
    RENAME = "rename"
    MOVE_METHOD = "move_method"
    INLINE_METHOD = "inline_method"
    REMOVE_DUPLICATION = "remove_duplication"
    SIMPLIFY_CONDITIONAL = "simplify_conditional"
    OPTIMIZE_IMPORTS = "optimize_imports"
    FORMAT_CODE = "format_code"
    ADD_TYPE_HINTS = "add_type_hints"
    MODERNIZE_SYNTAX = "modernize_syntax"
    IMPROVE_PERFORMANCE = "improve_performance"

@dataclass
class RefactoringOperation:
    """Represents a refactoring operation."""
    operation_type: RefactoringType
    description: str
    original_code: str
    refactored_code: str
    line_start: int
    line_end: int
    confidence: float
    benefits: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RefactoringResult:
    """Result of refactoring analysis."""
    original_code: str
    refactored_code: str
    operations: List[RefactoringOperation]
    improvements: Dict[str, float]
    warnings: List[str]
    processing_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class RefactoringEngine:
    """
    Intelligent code refactoring engine with pattern recognition.
    
    Features:
    - Automated refactoring suggestions
    - Code quality improvements
    - Performance optimizations
    - Modern syntax adoption
    - Type hint addition
    - Import optimization
    """
    
    def __init__(self, code_analyzer: Optional[CodeAnalyzer] = None):
        """Initialize the refactoring engine."""
        self.code_analyzer = code_analyzer or CodeAnalyzer()
        self.refactoring_patterns = self._initialize_refactoring_patterns()
        self.optimization_rules = self._initialize_optimization_rules()
        
        logger.info("Refactoring engine initialized")
    
    def _initialize_refactoring_patterns(self) -> Dict[CodeLanguage, Dict[RefactoringType, List[Dict[str, Any]]]]:
        """Initialize refactoring patterns for different languages."""
        return {
            CodeLanguage.PYTHON: {
                RefactoringType.OPTIMIZE_IMPORTS: [
                    {
                        "pattern": r"^import\s+(\w+)\nimport\s+(\w+)",
                        "replacement": r"import \1, \2",
                        "description": "Combine multiple import statements",
                        "confidence": 0.9
                    }
                ],
                RefactoringType.MODERNIZE_SYNTAX: [
                    {
                        "pattern": r"\.format\(",
                        "replacement": "f-string",
                        "description": "Replace .format() with f-strings",
                        "confidence": 0.8
                    },
                    {
                        "pattern": r"%\s*\(",
                        "replacement": "f-string",
                        "description": "Replace % formatting with f-strings",
                        "confidence": 0.8
                    }
                ],
                RefactoringType.IMPROVE_PERFORMANCE: [
                    {
                        "pattern": r"for\s+(\w+)\s+in\s+range\s*\(\s*len\s*\((\w+)\)\s*\):",
                        "replacement": r"for \1, item in enumerate(\2):",
                        "description": "Use enumerate instead of range(len())",
                        "confidence": 0.9
                    }
                ],
                RefactoringType.SIMPLIFY_CONDITIONAL: [
                    {
                        "pattern": r"if\s+(\w+)\s*==\s*True:",
                        "replacement": r"if \1:",
                        "description": "Simplify boolean comparison",
                        "confidence": 0.95
                    },
                    {
                        "pattern": r"if\s+(\w+)\s*==\s*False:",
                        "replacement": r"if not \1:",
                        "description": "Simplify boolean comparison",
                        "confidence": 0.95
                    }
                ]
            },
            CodeLanguage.JAVASCRIPT: {
                RefactoringType.MODERNIZE_SYNTAX: [
                    {
                        "pattern": r"function\s+(\w+)\s*\(",
                        "replacement": r"const \1 = (",
                        "description": "Convert function declaration to arrow function",
                        "confidence": 0.7
                    }
                ],
                RefactoringType.IMPROVE_PERFORMANCE: [
                    {
                        "pattern": r"document\.getElementById\s*\(\s*['\"](\w+)['\"]\s*\)",
                        "replacement": "cached reference",
                        "description": "Cache DOM element references",
                        "confidence": 0.8
                    }
                ]
            }
        }
    
    def _initialize_optimization_rules(self) -> Dict[CodeLanguage, List[Dict[str, Any]]]:
        """Initialize optimization rules for different languages."""
        return {
            CodeLanguage.PYTHON: [
                {
                    "name": "list_comprehension",
                    "pattern": r"(\w+)\s*=\s*\[\]\s*\nfor\s+(\w+)\s+in\s+(\w+):\s*\n\s*\1\.append\(([^)]+)\)",
                    "replacement": r"\1 = [\4 for \2 in \3]",
                    "description": "Convert loop to list comprehension",
                    "performance_gain": 0.3
                },
                {
                    "name": "set_membership",
                    "pattern": r"(\w+)\s+in\s+\[([^\]]+)\]",
                    "replacement": r"\1 in {\2}",
                    "description": "Use set for membership testing",
                    "performance_gain": 0.5
                }
            ],
            CodeLanguage.JAVASCRIPT: [
                {
                    "name": "const_declaration",
                    "pattern": r"var\s+(\w+)\s*=\s*([^;]+);",
                    "replacement": r"const \1 = \2;",
                    "description": "Use const for immutable variables",
                    "performance_gain": 0.1
                }
            ]
        }
    
    async def refactor_code(self, code: str, file_path: str = "", 
                          refactoring_types: Optional[List[RefactoringType]] = None) -> RefactoringResult:
        """
        Analyze code and suggest refactoring operations.
        
        Args:
            code: Source code to refactor
            file_path: Optional file path for context
            refactoring_types: Specific refactoring types to apply
            
        Returns:
            Refactoring result with suggestions
        """
        start_time = time.time()
        
        try:
            # Analyze the code first
            analysis = await self.code_analyzer.analyze_code(code, file_path)
            
            # Generate refactoring operations
            operations = []
            
            if not refactoring_types:
                refactoring_types = list(RefactoringType)
            
            for refactoring_type in refactoring_types:
                type_operations = await self._generate_refactoring_operations(
                    code, analysis.language, refactoring_type
                )
                operations.extend(type_operations)
            
            # Apply refactoring operations
            refactored_code = self._apply_refactoring_operations(code, operations)
            
            # Calculate improvements
            improvements = await self._calculate_improvements(code, refactored_code, analysis.language)
            
            # Generate warnings
            warnings = self._generate_warnings(operations)
            
            processing_time = time.time() - start_time
            
            return RefactoringResult(
                original_code=code,
                refactored_code=refactored_code,
                operations=operations,
                improvements=improvements,
                warnings=warnings,
                processing_time=processing_time,
                metadata={
                    "language": analysis.language.value,
                    "operations_count": len(operations),
                    "total_confidence": sum(op.confidence for op in operations) / len(operations) if operations else 0
                }
            )
            
        except Exception as e:
            logger.error(f"Refactoring failed: {e}")
            
            return RefactoringResult(
                original_code=code,
                refactored_code=code,
                operations=[],
                improvements={},
                warnings=[f"Refactoring failed: {str(e)}"],
                processing_time=time.time() - start_time,
                metadata={"error": str(e)}
            )
    
    async def _generate_refactoring_operations(self, code: str, language: CodeLanguage, 
                                             refactoring_type: RefactoringType) -> List[RefactoringOperation]:
        """Generate refactoring operations for a specific type."""
        operations = []
        
        # Get patterns for this language and refactoring type
        language_patterns = self.refactoring_patterns.get(language, {})
        type_patterns = language_patterns.get(refactoring_type, [])
        
        for pattern_info in type_patterns:
            pattern = pattern_info["pattern"]
            matches = re.finditer(pattern, code, re.MULTILINE)
            
            for match in matches:
                operation = await self._create_refactoring_operation(
                    code, match, pattern_info, refactoring_type
                )
                if operation:
                    operations.append(operation)
        
        # Add language-specific operations
        if language == CodeLanguage.PYTHON:
            operations.extend(await self._generate_python_specific_operations(code, refactoring_type))
        elif language == CodeLanguage.JAVASCRIPT:
            operations.extend(await self._generate_javascript_specific_operations(code, refactoring_type))
        
        return operations
    
    async def _create_refactoring_operation(self, code: str, match: re.Match, 
                                          pattern_info: Dict[str, Any], 
                                          refactoring_type: RefactoringType) -> Optional[RefactoringOperation]:
        """Create a refactoring operation from a pattern match."""
        try:
            original_text = match.group(0)
            line_start = code[:match.start()].count('\n') + 1
            line_end = code[:match.end()].count('\n') + 1
            
            # Generate refactored code based on pattern
            refactored_text = self._apply_pattern_replacement(original_text, pattern_info)
            
            if refactored_text and refactored_text != original_text:
                return RefactoringOperation(
                    operation_type=refactoring_type,
                    description=pattern_info["description"],
                    original_code=original_text,
                    refactored_code=refactored_text,
                    line_start=line_start,
                    line_end=line_end,
                    confidence=pattern_info["confidence"],
                    benefits=self._get_refactoring_benefits(refactoring_type),
                    risks=self._get_refactoring_risks(refactoring_type)
                )
            
        except Exception as e:
            logger.warning(f"Failed to create refactoring operation: {e}")
        
        return None
    
    def _apply_pattern_replacement(self, text: str, pattern_info: Dict[str, Any]) -> str:
        """Apply pattern replacement to generate refactored code."""
        pattern = pattern_info["pattern"]
        replacement = pattern_info["replacement"]
        
        if replacement == "f-string":
            return self._convert_to_fstring(text)
        elif replacement == "cached reference":
            return self._suggest_dom_caching(text)
        else:
            return re.sub(pattern, replacement, text)
    
    def _convert_to_fstring(self, text: str) -> str:
        """Convert string formatting to f-string."""
        # Simple conversion for .format()
        if ".format(" in text:
            # This is a simplified conversion
            # In practice, this would need more sophisticated parsing
            return text.replace(".format(", "f").replace(")", "")
        
        # Simple conversion for % formatting
        if "%" in text and "(" in text:
            return f"f{text.split('%')[0]}{{...}}"
        
        return text
    
    def _suggest_dom_caching(self, text: str) -> str:
        """Suggest DOM element caching."""
        match = re.search(r"getElementById\s*\(\s*['\"](\w+)['\"]\s*\)", text)
        if match:
            element_id = match.group(1)
            return f"// Cache this: const {element_id}Element = {text}"
        return text
    
    async def _generate_python_specific_operations(self, code: str, 
                                                 refactoring_type: RefactoringType) -> List[RefactoringOperation]:
        """Generate Python-specific refactoring operations."""
        operations = []
        
        if refactoring_type == RefactoringType.ADD_TYPE_HINTS:
            operations.extend(await self._suggest_type_hints(code))
        elif refactoring_type == RefactoringType.EXTRACT_METHOD:
            operations.extend(await self._suggest_method_extraction(code))
        elif refactoring_type == RefactoringType.REMOVE_DUPLICATION:
            operations.extend(await self._detect_code_duplication(code))
        
        return operations
    
    async def _generate_javascript_specific_operations(self, code: str, 
                                                     refactoring_type: RefactoringType) -> List[RefactoringOperation]:
        """Generate JavaScript-specific refactoring operations."""
        operations = []
        
        if refactoring_type == RefactoringType.MODERNIZE_SYNTAX:
            operations.extend(await self._modernize_javascript(code))
        
        return operations
    
    async def _suggest_type_hints(self, code: str) -> List[RefactoringOperation]:
        """Suggest type hints for Python functions."""
        operations = []
        
        # Find function definitions without type hints
        pattern = r"def\s+(\w+)\s*\(([^)]*)\)\s*:"
        matches = re.finditer(pattern, code)
        
        for match in matches:
            func_name = match.group(1)
            params = match.group(2)
            
            # Check if already has type hints
            if ":" not in params and "->" not in code[match.end():match.end()+50]:
                line_num = code[:match.start()].count('\n') + 1
                
                # Suggest basic type hints
                suggested_params = self._suggest_parameter_types(params)
                suggested_return = " -> None"  # Default suggestion
                
                original = match.group(0)
                refactored = f"def {func_name}({suggested_params}){suggested_return}:"
                
                operation = RefactoringOperation(
                    operation_type=RefactoringType.ADD_TYPE_HINTS,
                    description=f"Add type hints to function '{func_name}'",
                    original_code=original,
                    refactored_code=refactored,
                    line_start=line_num,
                    line_end=line_num,
                    confidence=0.6,
                    benefits=["Improved code documentation", "Better IDE support", "Type checking"],
                    risks=["May need adjustment based on actual types"]
                )
                operations.append(operation)
        
        return operations
    
    def _suggest_parameter_types(self, params: str) -> str:
        """Suggest types for function parameters."""
        if not params.strip():
            return params
        
        # Simple heuristic-based type suggestions
        param_list = [p.strip() for p in params.split(',')]
        typed_params = []
        
        for param in param_list:
            if '=' in param:
                name, default = param.split('=', 1)
                name = name.strip()
                default = default.strip()
                
                # Guess type from default value
                if default.startswith('"') or default.startswith("'"):
                    typed_params.append(f"{name}: str = {default}")
                elif default.isdigit():
                    typed_params.append(f"{name}: int = {default}")
                elif default in ['True', 'False']:
                    typed_params.append(f"{name}: bool = {default}")
                else:
                    typed_params.append(f"{name}: Any = {default}")
            else:
                typed_params.append(f"{param}: Any")
        
        return ', '.join(typed_params)
    
    async def _suggest_method_extraction(self, code: str) -> List[RefactoringOperation]:
        """Suggest method extraction for long functions."""
        operations = []
        
        # This would analyze function length and complexity
        # For now, return empty list
        return operations
    
    async def _detect_code_duplication(self, code: str) -> List[RefactoringOperation]:
        """Detect and suggest fixes for code duplication."""
        operations = []
        
        # This would use more sophisticated duplication detection
        # For now, return empty list
        return operations
    
    async def _modernize_javascript(self, code: str) -> List[RefactoringOperation]:
        """Modernize JavaScript syntax."""
        operations = []
        
        # Convert var to const/let
        var_pattern = r"var\s+(\w+)\s*=\s*([^;]+);"
        matches = re.finditer(var_pattern, code)
        
        for match in matches:
            var_name = match.group(1)
            value = match.group(2)
            line_num = code[:match.start()].count('\n') + 1
            
            # Suggest const if value looks immutable
            if any(keyword in value for keyword in ['function', 'require', 'import']):
                replacement = f"const {var_name} = {value};"
            else:
                replacement = f"let {var_name} = {value};"
            
            operation = RefactoringOperation(
                operation_type=RefactoringType.MODERNIZE_SYNTAX,
                description=f"Replace 'var' with 'const/let' for '{var_name}'",
                original_code=match.group(0),
                refactored_code=replacement,
                line_start=line_num,
                line_end=line_num,
                confidence=0.8,
                benefits=["Modern syntax", "Better scoping", "Prevents accidental reassignment"],
                risks=["May need adjustment if variable is reassigned"]
            )
            operations.append(operation)
        
        return operations
    
    def _apply_refactoring_operations(self, code: str, operations: List[RefactoringOperation]) -> str:
        """Apply refactoring operations to code."""
        refactored_code = code
        
        # Sort operations by line number (reverse order to avoid offset issues)
        sorted_operations = sorted(operations, key=lambda op: op.line_start, reverse=True)
        
        for operation in sorted_operations:
            try:
                refactored_code = refactored_code.replace(
                    operation.original_code, 
                    operation.refactored_code, 
                    1  # Replace only first occurrence
                )
            except Exception as e:
                logger.warning(f"Failed to apply refactoring operation: {e}")
        
        return refactored_code
    
    async def _calculate_improvements(self, original_code: str, refactored_code: str, 
                                    language: CodeLanguage) -> Dict[str, float]:
        """Calculate improvements from refactoring."""
        improvements = {}
        
        # Calculate basic metrics
        original_lines = len(original_code.splitlines())
        refactored_lines = len(refactored_code.splitlines())
        
        if original_lines > 0:
            improvements["line_reduction"] = (original_lines - refactored_lines) / original_lines * 100
        
        # Estimate readability improvement (simplified)
        improvements["readability"] = 5.0  # Placeholder
        
        # Estimate performance improvement (simplified)
        improvements["performance"] = 2.0  # Placeholder
        
        # Estimate maintainability improvement (simplified)
        improvements["maintainability"] = 8.0  # Placeholder
        
        return improvements
    
    def _generate_warnings(self, operations: List[RefactoringOperation]) -> List[str]:
        """Generate warnings about refactoring operations."""
        warnings = []
        
        high_risk_operations = [op for op in operations if any("risk" in risk.lower() for risk in op.risks)]
        
        if high_risk_operations:
            warnings.append(f"{len(high_risk_operations)} operations have potential risks")
        
        low_confidence_operations = [op for op in operations if op.confidence < 0.7]
        
        if low_confidence_operations:
            warnings.append(f"{len(low_confidence_operations)} operations have low confidence")
        
        if len(operations) > 20:
            warnings.append("Large number of refactoring operations - consider applying incrementally")
        
        return warnings
    
    def _get_refactoring_benefits(self, refactoring_type: RefactoringType) -> List[str]:
        """Get benefits for a refactoring type."""
        benefits_map = {
            RefactoringType.OPTIMIZE_IMPORTS: ["Cleaner imports", "Reduced clutter"],
            RefactoringType.MODERNIZE_SYNTAX: ["Modern syntax", "Better performance", "Improved readability"],
            RefactoringType.IMPROVE_PERFORMANCE: ["Better performance", "More efficient code"],
            RefactoringType.SIMPLIFY_CONDITIONAL: ["Improved readability", "Cleaner logic"],
            RefactoringType.ADD_TYPE_HINTS: ["Better documentation", "IDE support", "Type checking"],
            RefactoringType.EXTRACT_METHOD: ["Better organization", "Reusability", "Easier testing"],
            RefactoringType.REMOVE_DUPLICATION: ["DRY principle", "Easier maintenance", "Reduced bugs"]
        }
        
        return benefits_map.get(refactoring_type, ["Code improvement"])
    
    def _get_refactoring_risks(self, refactoring_type: RefactoringType) -> List[str]:
        """Get risks for a refactoring type."""
        risks_map = {
            RefactoringType.OPTIMIZE_IMPORTS: ["May affect import order dependencies"],
            RefactoringType.MODERNIZE_SYNTAX: ["May require testing for compatibility"],
            RefactoringType.IMPROVE_PERFORMANCE: ["May change behavior in edge cases"],
            RefactoringType.ADD_TYPE_HINTS: ["May need adjustment for actual types"],
            RefactoringType.EXTRACT_METHOD: ["May affect variable scope"],
            RefactoringType.REMOVE_DUPLICATION: ["May introduce coupling"]
        }
        
        return risks_map.get(refactoring_type, ["Minimal risk"])
