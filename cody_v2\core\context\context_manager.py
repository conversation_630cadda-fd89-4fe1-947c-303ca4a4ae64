#!/usr/bin/env python3

"""
Context Manager for CODY v2.0
Advanced context tracking and management for intelligent assistance
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import deque
import threading

logger = logging.getLogger('CODY.ContextManager')

class ContextType(Enum):
    """Types of context information."""
    USER_INTENT = "user_intent"
    PROJECT_STATE = "project_state"
    FILE_CONTEXT = "file_context"
    TERMINAL_STATE = "terminal_state"
    CONVERSATION = "conversation"
    WORKFLOW = "workflow"
    ENVIRONMENT = "environment"
    PREFERENCES = "preferences"

class ContextScope(Enum):
    """Scope of context information."""
    SESSION = "session"
    PROJECT = "project"
    GLOBAL = "global"
    TEMPORARY = "temporary"

@dataclass
class ContextEntry:
    """Represents a single context entry."""
    id: str
    context_type: ContextType
    scope: ContextScope
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    ttl: Optional[float] = None  # Time to live in seconds
    priority: int = 1  # Higher number = higher priority
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ContextSnapshot:
    """Snapshot of context at a point in time."""
    timestamp: float
    entries: List[ContextEntry]
    summary: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)

class ContextManager:
    """
    Advanced context manager for intelligent assistance.
    
    Features:
    - Multi-scope context tracking (session, project, global)
    - Automatic context expiration and cleanup
    - Context prioritization and relevance scoring
    - Context snapshots and history
    - Intelligent context suggestions
    - Cross-session context persistence
    """
    
    def __init__(self, max_entries: int = 1000, cleanup_interval: float = 300.0):
        """Initialize the context manager."""
        self.max_entries = max_entries
        self.cleanup_interval = cleanup_interval
        
        # Context storage
        self.contexts: Dict[str, ContextEntry] = {}
        self.context_history: deque = deque(maxlen=100)
        
        # Indexing for fast retrieval
        self.type_index: Dict[ContextType, List[str]] = {ct: [] for ct in ContextType}
        self.scope_index: Dict[ContextScope, List[str]] = {cs: [] for cs in ContextScope}
        
        # Threading
        self.lock = threading.RLock()
        self.cleanup_thread = None
        self.shutdown_event = threading.Event()
        
        # Start cleanup thread
        self._start_cleanup_thread()
        
        logger.info("Context manager initialized")
    
    def _start_cleanup_thread(self):
        """Start background cleanup thread."""
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True,
            name="ContextCleanup"
        )
        self.cleanup_thread.start()
    
    def _cleanup_loop(self):
        """Background cleanup loop."""
        while not self.shutdown_event.is_set():
            try:
                self._cleanup_expired_contexts()
                self.shutdown_event.wait(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Error in context cleanup: {e}")
    
    def add_context(self, 
                   context_type: ContextType,
                   scope: ContextScope,
                   data: Dict[str, Any],
                   ttl: Optional[float] = None,
                   priority: int = 1,
                   context_id: Optional[str] = None) -> str:
        """
        Add context information.
        
        Args:
            context_type: Type of context
            scope: Scope of context
            data: Context data
            ttl: Time to live in seconds
            priority: Priority level
            context_id: Optional custom ID
            
        Returns:
            Context ID
        """
        with self.lock:
            # Generate ID if not provided
            if not context_id:
                context_id = f"{context_type.value}_{scope.value}_{int(time.time() * 1000)}"
            
            # Create context entry
            entry = ContextEntry(
                id=context_id,
                context_type=context_type,
                scope=scope,
                data=data.copy(),
                ttl=ttl,
                priority=priority,
                metadata={
                    "created_by": "context_manager",
                    "version": "1.0"
                }
            )
            
            # Store context
            self.contexts[context_id] = entry
            
            # Update indexes
            self.type_index[context_type].append(context_id)
            self.scope_index[scope].append(context_id)
            
            # Maintain size limit
            self._enforce_size_limit()
            
            logger.debug(f"Added context: {context_id} ({context_type.value})")
            return context_id
    
    def get_context(self, context_id: str) -> Optional[ContextEntry]:
        """Get context by ID."""
        with self.lock:
            return self.contexts.get(context_id)
    
    def get_contexts_by_type(self, context_type: ContextType, 
                           limit: Optional[int] = None) -> List[ContextEntry]:
        """Get contexts by type."""
        with self.lock:
            context_ids = self.type_index.get(context_type, [])
            contexts = [self.contexts[cid] for cid in context_ids if cid in self.contexts]
            
            # Sort by priority and timestamp
            contexts.sort(key=lambda x: (x.priority, x.timestamp), reverse=True)
            
            if limit:
                contexts = contexts[:limit]
            
            return contexts
    
    def get_contexts_by_scope(self, scope: ContextScope,
                            limit: Optional[int] = None) -> List[ContextEntry]:
        """Get contexts by scope."""
        with self.lock:
            context_ids = self.scope_index.get(scope, [])
            contexts = [self.contexts[cid] for cid in context_ids if cid in self.contexts]
            
            # Sort by priority and timestamp
            contexts.sort(key=lambda x: (x.priority, x.timestamp), reverse=True)
            
            if limit:
                contexts = contexts[:limit]
            
            return contexts
    
    def search_contexts(self, 
                       query: str,
                       context_types: Optional[List[ContextType]] = None,
                       scopes: Optional[List[ContextScope]] = None,
                       limit: int = 10) -> List[ContextEntry]:
        """
        Search contexts by query.
        
        Args:
            query: Search query
            context_types: Filter by context types
            scopes: Filter by scopes
            limit: Maximum results
            
        Returns:
            Matching contexts
        """
        with self.lock:
            matching_contexts = []
            query_lower = query.lower()
            
            for context in self.contexts.values():
                # Filter by type
                if context_types and context.context_type not in context_types:
                    continue
                
                # Filter by scope
                if scopes and context.scope not in scopes:
                    continue
                
                # Search in data
                context_text = json.dumps(context.data).lower()
                if query_lower in context_text:
                    matching_contexts.append(context)
            
            # Sort by relevance (simplified)
            matching_contexts.sort(key=lambda x: x.priority, reverse=True)
            
            return matching_contexts[:limit]
    
    def update_context(self, context_id: str, data: Dict[str, Any]) -> bool:
        """Update context data."""
        with self.lock:
            if context_id in self.contexts:
                self.contexts[context_id].data.update(data)
                self.contexts[context_id].timestamp = time.time()
                return True
            return False
    
    def remove_context(self, context_id: str) -> bool:
        """Remove context by ID."""
        with self.lock:
            if context_id in self.contexts:
                context = self.contexts[context_id]
                
                # Remove from indexes
                self.type_index[context.context_type].remove(context_id)
                self.scope_index[context.scope].remove(context_id)
                
                # Remove from storage
                del self.contexts[context_id]
                
                logger.debug(f"Removed context: {context_id}")
                return True
            return False
    
    def clear_scope(self, scope: ContextScope):
        """Clear all contexts in a scope."""
        with self.lock:
            context_ids = self.scope_index[scope].copy()
            for context_id in context_ids:
                self.remove_context(context_id)
            
            logger.info(f"Cleared {len(context_ids)} contexts from scope: {scope.value}")
    
    def create_snapshot(self, name: str = "") -> ContextSnapshot:
        """Create a snapshot of current context."""
        with self.lock:
            snapshot = ContextSnapshot(
                timestamp=time.time(),
                entries=list(self.contexts.values()),
                summary=self._generate_context_summary(),
                metadata={"name": name, "total_contexts": len(self.contexts)}
            )
            
            self.context_history.append(snapshot)
            return snapshot
    
    def _generate_context_summary(self) -> Dict[str, Any]:
        """Generate summary of current context."""
        summary = {
            "total_contexts": len(self.contexts),
            "by_type": {},
            "by_scope": {},
            "recent_activity": []
        }
        
        # Count by type
        for context_type in ContextType:
            count = len(self.type_index[context_type])
            if count > 0:
                summary["by_type"][context_type.value] = count
        
        # Count by scope
        for scope in ContextScope:
            count = len(self.scope_index[scope])
            if count > 0:
                summary["by_scope"][scope.value] = count
        
        # Recent activity
        recent_contexts = sorted(
            self.contexts.values(),
            key=lambda x: x.timestamp,
            reverse=True
        )[:5]
        
        summary["recent_activity"] = [
            {
                "id": ctx.id,
                "type": ctx.context_type.value,
                "timestamp": ctx.timestamp
            }
            for ctx in recent_contexts
        ]
        
        return summary
    
    def get_relevant_context(self, 
                           query: str,
                           context_types: Optional[List[ContextType]] = None,
                           max_contexts: int = 5) -> List[ContextEntry]:
        """
        Get most relevant context for a query.
        
        Args:
            query: Query to find relevant context for
            context_types: Preferred context types
            max_contexts: Maximum contexts to return
            
        Returns:
            Most relevant contexts
        """
        # Search for matching contexts
        matching_contexts = self.search_contexts(
            query=query,
            context_types=context_types,
            limit=max_contexts * 2  # Get more to filter
        )
        
        # Score contexts by relevance
        scored_contexts = []
        for context in matching_contexts:
            score = self._calculate_relevance_score(context, query)
            scored_contexts.append((context, score))
        
        # Sort by score and return top results
        scored_contexts.sort(key=lambda x: x[1], reverse=True)
        return [ctx for ctx, score in scored_contexts[:max_contexts]]
    
    def _calculate_relevance_score(self, context: ContextEntry, query: str) -> float:
        """Calculate relevance score for context."""
        score = 0.0
        query_lower = query.lower()
        
        # Base score from priority
        score += context.priority * 0.1
        
        # Recency score (more recent = higher score)
        age = time.time() - context.timestamp
        recency_score = max(0, 1 - (age / 3600))  # Decay over 1 hour
        score += recency_score * 0.3
        
        # Content relevance
        context_text = json.dumps(context.data).lower()
        query_words = query_lower.split()
        
        for word in query_words:
            if word in context_text:
                score += 0.2
        
        # Type-specific bonuses
        if context.context_type == ContextType.USER_INTENT:
            score += 0.1
        elif context.context_type == ContextType.PROJECT_STATE:
            score += 0.15
        
        return score
    
    def _cleanup_expired_contexts(self):
        """Clean up expired contexts."""
        with self.lock:
            current_time = time.time()
            expired_ids = []
            
            for context_id, context in self.contexts.items():
                if context.ttl and (current_time - context.timestamp) > context.ttl:
                    expired_ids.append(context_id)
            
            for context_id in expired_ids:
                self.remove_context(context_id)
            
            if expired_ids:
                logger.info(f"Cleaned up {len(expired_ids)} expired contexts")
    
    def _enforce_size_limit(self):
        """Enforce maximum number of contexts."""
        if len(self.contexts) > self.max_entries:
            # Remove oldest, lowest priority contexts
            contexts_to_remove = sorted(
                self.contexts.values(),
                key=lambda x: (x.priority, x.timestamp)
            )
            
            num_to_remove = len(self.contexts) - self.max_entries
            for context in contexts_to_remove[:num_to_remove]:
                self.remove_context(context.id)
            
            logger.info(f"Removed {num_to_remove} contexts to enforce size limit")
    
    def get_context_stats(self) -> Dict[str, Any]:
        """Get context manager statistics."""
        with self.lock:
            return {
                "total_contexts": len(self.contexts),
                "by_type": {ct.value: len(ids) for ct, ids in self.type_index.items() if ids},
                "by_scope": {cs.value: len(ids) for cs, ids in self.scope_index.items() if ids},
                "snapshots_count": len(self.context_history),
                "max_entries": self.max_entries,
                "cleanup_interval": self.cleanup_interval
            }
    
    def export_contexts(self, scope: Optional[ContextScope] = None) -> Dict[str, Any]:
        """Export contexts for persistence."""
        with self.lock:
            contexts_to_export = []
            
            for context in self.contexts.values():
                if scope is None or context.scope == scope:
                    contexts_to_export.append(asdict(context))
            
            return {
                "contexts": contexts_to_export,
                "export_timestamp": time.time(),
                "version": "1.0"
            }
    
    def import_contexts(self, data: Dict[str, Any]) -> int:
        """Import contexts from exported data."""
        imported_count = 0
        
        try:
            contexts_data = data.get("contexts", [])
            
            for context_data in contexts_data:
                # Recreate context entry
                entry = ContextEntry(
                    id=context_data["id"],
                    context_type=ContextType(context_data["context_type"]),
                    scope=ContextScope(context_data["scope"]),
                    data=context_data["data"],
                    timestamp=context_data["timestamp"],
                    ttl=context_data.get("ttl"),
                    priority=context_data.get("priority", 1),
                    metadata=context_data.get("metadata", {})
                )
                
                # Add to storage
                with self.lock:
                    self.contexts[entry.id] = entry
                    self.type_index[entry.context_type].append(entry.id)
                    self.scope_index[entry.scope].append(entry.id)
                
                imported_count += 1
            
            logger.info(f"Imported {imported_count} contexts")
            
        except Exception as e:
            logger.error(f"Failed to import contexts: {e}")
        
        return imported_count
    
    def shutdown(self):
        """Shutdown the context manager."""
        logger.info("Shutting down context manager")
        self.shutdown_event.set()
        
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5.0)
        
        logger.info("Context manager shutdown complete")
