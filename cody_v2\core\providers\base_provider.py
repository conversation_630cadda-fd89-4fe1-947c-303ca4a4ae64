#!/usr/bin/env python3

"""
Base AI Provider Interface for CODY v2.0
Abstract interface for all AI model providers
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
import json

logger = logging.getLogger('CODY.Providers')

class ModelType(Enum):
    """Types of AI models."""
    CHAT = "chat"
    COMPLETION = "completion"
    REASONING = "reasoning"
    CODE = "code"
    EMBEDDING = "embedding"

class ProviderStatus(Enum):
    """Provider operational status."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"
    RATE_LIMITED = "rate_limited"
    MAINTENANCE = "maintenance"

@dataclass
class ModelInfo:
    """Information about an AI model."""
    name: str
    display_name: str
    model_type: ModelType
    max_tokens: int
    supports_streaming: bool = True
    supports_function_calling: bool = False
    supports_vision: bool = False
    cost_per_token: float = 0.0
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProviderConfig:
    """Configuration for an AI provider."""
    api_key: str
    base_url: Optional[str] = None
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_per_minute: int = 60
    enable_caching: bool = True
    cache_ttl: int = 3600
    custom_headers: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ChatMessage:
    """Represents a chat message."""
    role: str  # "user", "assistant", "system"
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ChatRequest:
    """Request for chat completion."""
    messages: List[ChatMessage]
    model: str
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 1.0
    stream: bool = False
    stop: Optional[List[str]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ChatResponse:
    """Response from chat completion."""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProviderMetrics:
    """Metrics for provider performance."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    total_tokens_used: int = 0
    rate_limit_hits: int = 0
    last_request_time: float = 0.0
    uptime_percentage: float = 100.0

class BaseAIProvider(ABC):
    """
    Abstract base class for all AI providers.
    Defines the interface that all providers must implement.
    """
    
    def __init__(self, 
                 provider_name: str,
                 config: ProviderConfig):
        """
        Initialize the AI provider.
        
        Args:
            provider_name: Name of the provider
            config: Provider configuration
        """
        self.provider_name = provider_name
        self.config = config
        self.status = ProviderStatus.UNAVAILABLE
        self.metrics = ProviderMetrics()
        self.models: Dict[str, ModelInfo] = {}
        
        # Rate limiting
        self.request_times: List[float] = []
        
        # Initialize provider
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the provider."""
        try:
            self.initialize()
            self.status = ProviderStatus.AVAILABLE
            logger.info(f"Provider {self.provider_name} initialized successfully")
        except Exception as e:
            self.status = ProviderStatus.ERROR
            logger.error(f"Failed to initialize provider {self.provider_name}: {e}")
            raise
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize provider-specific components. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    async def chat_completion(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion.
        
        Args:
            request: Chat completion request
            
        Returns:
            Chat completion response
        """
        pass
    
    @abstractmethod
    async def stream_chat_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Generate streaming chat completion.
        
        Args:
            request: Chat completion request
            
        Yields:
            Partial response chunks
        """
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[ModelInfo]:
        """Get list of available models for this provider."""
        pass
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.models.get(model_name)
    
    def is_available(self) -> bool:
        """Check if the provider is available."""
        return self.status == ProviderStatus.AVAILABLE
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits."""
        current_time = time.time()
        
        # Remove requests older than 1 minute
        self.request_times = [
            t for t in self.request_times 
            if current_time - t < 60
        ]
        
        # Check if we're within rate limit
        if len(self.request_times) >= self.config.rate_limit_per_minute:
            self.metrics.rate_limit_hits += 1
            return False
        
        return True
    
    def _record_request(self) -> None:
        """Record a new request for rate limiting."""
        self.request_times.append(time.time())
        self.metrics.total_requests += 1
        self.metrics.last_request_time = time.time()
    
    def _record_success(self, response_time: float, tokens_used: int = 0) -> None:
        """Record a successful request."""
        self.metrics.successful_requests += 1
        self.metrics.total_tokens_used += tokens_used
        
        # Update average response time
        total_successful = self.metrics.successful_requests
        self.metrics.average_response_time = (
            (self.metrics.average_response_time * (total_successful - 1) + response_time) 
            / total_successful
        )
    
    def _record_failure(self) -> None:
        """Record a failed request."""
        self.metrics.failed_requests += 1
    
    async def _execute_with_retry(self, operation, *args, **kwargs):
        """Execute an operation with retry logic."""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if not self._check_rate_limit():
                    raise Exception("Rate limit exceeded")
                
                self._record_request()
                start_time = time.time()
                
                result = await operation(*args, **kwargs)
                
                response_time = time.time() - start_time
                self._record_success(response_time)
                
                return result
                
            except Exception as e:
                last_exception = e
                self._record_failure()
                
                if attempt < self.config.max_retries:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                    logger.warning(f"Retry {attempt + 1} for {self.provider_name}: {e}")
                else:
                    logger.error(f"All retries failed for {self.provider_name}: {e}")
        
        raise last_exception
    
    def get_metrics(self) -> ProviderMetrics:
        """Get provider performance metrics."""
        # Calculate uptime percentage
        if self.metrics.total_requests > 0:
            self.metrics.uptime_percentage = (
                self.metrics.successful_requests / self.metrics.total_requests * 100
            )
        
        return self.metrics
    
    def get_status(self) -> Dict[str, Any]:
        """Get provider status information."""
        return {
            "provider_name": self.provider_name,
            "status": self.status.value,
            "available_models": len(self.models),
            "metrics": self.get_metrics().__dict__,
            "config": {
                "timeout": self.config.timeout,
                "max_retries": self.config.max_retries,
                "rate_limit_per_minute": self.config.rate_limit_per_minute
            }
        }
    
    def validate_request(self, request: ChatRequest) -> bool:
        """Validate a chat request."""
        if not request.messages:
            return False
        
        if request.model not in self.models:
            return False
        
        model_info = self.models[request.model]
        
        # Check token limits (simplified)
        total_content_length = sum(len(msg.content) for msg in request.messages)
        estimated_tokens = total_content_length // 4  # Rough estimation
        
        if estimated_tokens > model_info.max_tokens:
            return False
        
        return True
    
    def optimize_request(self, request: ChatRequest) -> ChatRequest:
        """Optimize request for this provider."""
        # Default implementation - subclasses can override
        return request
