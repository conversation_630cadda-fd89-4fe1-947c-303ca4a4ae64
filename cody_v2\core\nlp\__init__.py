#!/usr/bin/env python3

"""
Advanced Natural Language Processing for CODY v2.0
Multi-language NLP with context awareness and intent recognition
"""

from .advanced_nlp import (
    AdvancedNLPProcessor,
    Language,
    IntentCategory,
    Complexity,
    Entity,
    Intent,
    NLPResult
)

from .intent_analyzer import (
    IntentAnalyzer,
    ActionType,
    Priority,
    ActionPlan,
    WorkflowStep,
    IntentAnalysisResult
)

from .entity_extractor import (
    EntityExtractor,
    EntityType,
    ExtractedEntity
)

from .language_detector import (
    LanguageDetector,
    LanguageDetectionResult
)

__all__ = [
    # Advanced NLP
    'AdvancedNLPProcessor',
    'Language',
    'IntentCategory',
    'Complexity',
    'Entity',
    'Intent',
    'NLPResult',
    
    # Intent Analysis
    'IntentAnalyzer',
    'ActionType',
    'Priority',
    'ActionPlan',
    'WorkflowStep',
    'IntentAnalysisResult',
    
    # Entity Extraction
    'EntityExtractor',
    'EntityType',
    'ExtractedEntity',
    
    # Language Detection
    'LanguageDetector',
    'LanguageDetectionResult'
]

__version__ = "2.0.0"
__author__ = "CODY Development Team"
__description__ = "Advanced Natural Language Processing with multi-language support"
